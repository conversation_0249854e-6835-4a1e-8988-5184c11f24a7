using Core.DataAccess;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ISystemExerciseDal : IEntityRepository<SystemExercise>
    {
        List<SystemExerciseDto> GetAllSystemExercises();
        List<SystemExerciseDto> GetSystemExercisesByCategory(int categoryId);
        PaginatedResult<SystemExerciseDto> GetSystemExercisesFiltered(SystemExerciseFilterDto filter);
        List<SystemExerciseDto> SearchSystemExercises(string searchTerm);
        SystemExerciseDto GetSystemExerciseDetail(int exerciseId);

        // SOLID prensiplerine uygun: Validation ve soft delete logic DAL katmanında
        IResult SoftDeleteSystemExerciseWithValidation(int exerciseId);

        // SOLID prensiplerine uygun: Entity oluşturma ve tarih yönetimi DAL katmanında
        IResult AddSystemExerciseWithManagement(SystemExerciseAddDto exerciseAddDto);
        IResult UpdateSystemExerciseWithManagement(SystemExerciseUpdateDto exerciseUpdateDto);
    }
}
