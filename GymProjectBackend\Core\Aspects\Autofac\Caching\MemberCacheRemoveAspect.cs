using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Security.Claims;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Member rolündeki kullanıcılar için özel cache invalidation aspect
    /// JWT'den CompanyId'yi otomatik alır ve cross-service cache temizleme yapar
    /// </summary>
    public class MemberCacheRemoveAspect : MethodInterception
    {
        private readonly ICacheService _cacheService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public MemberCacheRemoveAspect()
        {
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _httpContextAccessor = ServiceTool.ServiceProvider.GetService<IHttpContextAccessor>();
        }

        public override void Intercept(IInvocation invocation)
        {
            // Metodu çalıştır
            invocation.Proceed();

            // Eğer metod başarılı olduysa cache temizleme yap
            if (IsMethodSuccessful(invocation))
            {
                var companyId = GetCompanyIdFromJWT();
                if (companyId > 0)
                {
                    InvalidateMemberRelatedCaches(companyId);
                }
            }
        }

        private bool IsMethodSuccessful(IInvocation invocation)
        {
            var result = invocation.ReturnValue;
            
            // IResult interface'ini kontrol et
            if (result != null && result.GetType().GetProperty("Success") != null)
            {
                var successProperty = result.GetType().GetProperty("Success");
                return (bool)successProperty.GetValue(result);
            }
            
            return true; // Default olarak başarılı kabul et
        }

        private int GetCompanyIdFromJWT()
        {
            try
            {
                var companyIdClaim = _httpContextAccessor.HttpContext?.User?.Claims?
                    .FirstOrDefault(c => c.Type == "CompanyId");

                if (companyIdClaim != null && int.TryParse(companyIdClaim.Value, out int companyId))
                {
                    return companyId;
                }

                return -1;
            }
            catch
            {
                return -1;
            }
        }

        private void InvalidateMemberRelatedCaches(int companyId)
        {
            try
            {
                // Member ile ilgili tüm cache pattern'leri
                var cachePatterns = new[]
                {
                    $"gym:{companyId}:imemberservice:*",
                    $"gym:{companyId}:imemberdetailsservice:*",
                    $"gym:{companyId}:imembershipservice:*",
                    $"gym:{companyId}:ipaymentservice:*",
                    $"gym:{companyId}:itransactionservice:*",
                    $"gym:{companyId}:iremainingdebtservice:*",
                    $"gym:{companyId}:imemberworkoutprogramservice:*"
                };

                foreach (var pattern in cachePatterns)
                {
                    _cacheService.RemoveByPattern(pattern);
                }

                System.Diagnostics.Debug.WriteLine($"Member cache invalidation completed for CompanyId: {companyId}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Member cache invalidation error: {ex.Message}");
                // Cache hatası uygulamayı durdurmasın
            }
        }
    }
}
