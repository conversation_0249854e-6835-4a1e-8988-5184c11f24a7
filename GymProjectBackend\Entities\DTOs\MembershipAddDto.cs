﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MembershipAddDto:IDto
    {
        public int MemberID { get; set; }
        public int MembershipTypeID { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string PaymentStatus { get; set; }
        public decimal Price { get; set; }
        public string PaymentMethod { get; set; }
        public int Day { get; set; }

    }
}
