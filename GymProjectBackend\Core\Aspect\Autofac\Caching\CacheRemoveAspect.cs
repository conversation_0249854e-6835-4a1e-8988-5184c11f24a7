using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// AOP Cache Remove Aspect - CUD işlemlerinde cache invalidation
    /// Multi-tenant aware, pattern-based cache temizleme
    /// Performance monitoring ve cache invalidation logging
    /// </summary>
    public class CacheRemoveAspect : MethodInterception
    {
        private readonly string[] _patterns;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;

        /// <summary>
        /// Cache remove aspect constructor
        /// </summary>
        /// <param name="patterns">Temizlenecek cache pattern'leri (örn: "gym:*:member:*")</param>
        public CacheRemoveAspect(params string[] patterns)
        {
            _patterns = patterns ?? throw new ArgumentNullException(nameof(patterns));
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
        }

        public override void Intercept(IInvocation invocation)
        {
            var methodName = $"{invocation.Method.ReflectedType.FullName}.{invocation.Method.Name}";
            
            try
            {
                // Önce method'u çalıştır
                invocation.Proceed();

                // Method başarılı olduysa cache'i temizle
                if (ShouldInvalidateCache(invocation))
                {
                    InvalidateCachePatterns(methodName);
                }
            }
            catch (Exception ex)
            {
                // Method hata verirse cache temizleme yapma
                LogCacheError(methodName, ex);
                throw;
            }
        }

        /// <summary>
        /// Cache invalidation yapılıp yapılmayacağını kontrol eder
        /// </summary>
        private bool ShouldInvalidateCache(IInvocation invocation)
        {
            // IResult dönen method'lar için Success kontrolü
            if (invocation.ReturnValue != null)
            {
                var returnType = invocation.ReturnValue.GetType();
                
                // IResult interface'ini implement ediyorsa Success property'sini kontrol et
                if (returnType.GetInterfaces().Any(i => i.Name == "IResult"))
                {
                    var successProperty = returnType.GetProperty("Success");
                    if (successProperty != null)
                    {
                        var success = (bool)successProperty.GetValue(invocation.ReturnValue);
                        return success;
                    }
                }
            }

            // Diğer durumlarda (void, primitive types) her zaman temizle
            return true;
        }

        /// <summary>
        /// Belirtilen pattern'lere göre cache'i temizler
        /// </summary>
        private void InvalidateCachePatterns(string methodName)
        {
            var companyId = _companyContext.GetCompanyId();
            var totalRemoved = 0;

            foreach (var pattern in _patterns)
            {
                try
                {
                    // Pattern'de {companyId} placeholder'ı varsa değiştir
                    var resolvedPattern = pattern.Replace("{companyId}", companyId.ToString());

                    // Wildcard pattern'leri destekle
                    var removedCount = _cacheService.RemoveByPattern(resolvedPattern);
                    totalRemoved += (int)removedCount;

                    LogCacheInvalidation(methodName, resolvedPattern, removedCount);
                }
                catch (Exception ex)
                {
                    LogCacheError(methodName, ex, pattern);
                }
            }

            LogTotalCacheInvalidation(methodName, totalRemoved);
        }

        /// <summary>
        /// Cache invalidation başarı log'u
        /// </summary>
        private void LogCacheInvalidation(string methodName, string pattern, long removedCount)
        {
            // Production'da console logging kaldırıldı
            // TODO: ILogService ile structured logging eklenebilir
        }

        /// <summary>
        /// Toplam cache invalidation log'u
        /// </summary>
        private void LogTotalCacheInvalidation(string methodName, int totalRemoved)
        {
            // Bu log'u göstermeyelim, çok detay
        }

        /// <summary>
        /// Cache invalidation hata log'u
        /// </summary>
        private void LogCacheError(string methodName, Exception ex, string pattern = null)
        {
            var patternInfo = pattern != null ? $", Pattern: {pattern}" : "";
            Console.WriteLine($"[CACHE REMOVE ERROR] Method: {methodName}{patternInfo}, Error: {ex.Message}");
        }
    }
}
