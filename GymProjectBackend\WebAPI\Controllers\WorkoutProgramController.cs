using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class WorkoutProgramController : ControllerBase
    {
        private readonly IWorkoutProgramTemplateService _workoutProgramTemplateService;

        public WorkoutProgramController(IWorkoutProgramTemplateService workoutProgramTemplateService)
        {
            _workoutProgramTemplateService = workoutProgramTemplateService;
        }

        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _workoutProgramTemplateService.GetAll();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getbyid")]
        public IActionResult GetById(int id)
        {
            var result = _workoutProgramTemplateService.GetById(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpPost("add")]
        public IActionResult Add([FromBody] WorkoutProgramTemplateAddDto templateAddDto)
        {
            var result = _workoutProgramTemplateService.Add(templateAddDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpPut("update")]
        public IActionResult Update([FromBody] WorkoutProgramTemplateUpdateDto templateUpdateDto)
        {
            var result = _workoutProgramTemplateService.Update(templateUpdateDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _workoutProgramTemplateService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}
