﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class UserCompanyDetailDto:IDto
    {
        public int UserCompanyId { get; set; }
        public string CompanyUserName { get; set; }
        public string CompanyName { get; set; }
        public bool? isActive{ get; set; }
    }
}
