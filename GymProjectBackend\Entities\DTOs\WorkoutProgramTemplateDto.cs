using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    public class WorkoutProgramTemplateDto : IDto
    {
        public int WorkoutProgramTemplateID { get; set; }
        public int CompanyID { get; set; }
        public string ProgramName { get; set; }
        public string? Description { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public int DayCount { get; set; }
        public List<WorkoutProgramDayDto> Days { get; set; } = new List<WorkoutProgramDayDto>();
    }

    public class WorkoutProgramTemplateAddDto : IDto
    {
        public string ProgramName { get; set; }
        public string? Description { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public List<WorkoutProgramDayAddDto> Days { get; set; } = new List<WorkoutProgramDayAddDto>();
    }

    public class WorkoutProgramTemplateUpdateDto : IDto
    {
        public int WorkoutProgramTemplateID { get; set; }
        public string ProgramName { get; set; }
        public string? Description { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public bool? IsActive { get; set; }
        public List<WorkoutProgramDayUpdateDto> Days { get; set; } = new List<WorkoutProgramDayUpdateDto>();
    }

    public class WorkoutProgramTemplateListDto : IDto
    {
        public int WorkoutProgramTemplateID { get; set; }
        public string ProgramName { get; set; }
        public string? Description { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public int DayCount { get; set; }
        public int ExerciseCount { get; set; }
    }
}
