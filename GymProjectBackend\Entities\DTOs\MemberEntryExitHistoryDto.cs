﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MemberEntryExitHistoryDto:IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public DateTime? EntryDate { get; set; }
        public bool? IsActive { get; set; }
    }
}
