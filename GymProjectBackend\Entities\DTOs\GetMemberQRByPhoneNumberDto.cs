﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class GetMemberQRByPhoneNumberDto : IDto
    {
        public string Name { get; set; }
        public string ScanNumber { get; set; }
        public string RemainingDays { get; set; }
        public List<MembershipInfo> Memberships { get; set; }
        public bool IsFrozen { get; set; }
        public DateTime? FreezeEndDate { get; set; }
        public string Message { get; set; } // Eksik özellik eklendi
        public string PhoneNumber { get; set; } // Telefon numarası eklendi
    }
    public class MembershipInfo:IDto
    {
        public string Branch { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
    }


}
