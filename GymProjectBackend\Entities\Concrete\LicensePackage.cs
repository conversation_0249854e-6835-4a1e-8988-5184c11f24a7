﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.Concrete
{
    public class LicensePackage : IEntity
    {
        [Key]
        public int LicensePackageID { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Role { get; set; } 
        public int DurationDays { get; set; } 
        public decimal Price { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? DeletedDate { get; set; }
    }
}
