using Microsoft.AspNetCore.Mvc;
using StackExchange.Redis;
using System.Text.Json;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RedisTestController : ControllerBase
    {
        private readonly IDatabase _database;
        private readonly IConnectionMultiplexer _redis;

        public RedisTestController(IDatabase database, IConnectionMultiplexer redis)
        {
            _database = database;
            _redis = redis;
        }

        [HttpGet("ping")]
        public async Task<IActionResult> Ping()
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                var pong = await server.PingAsync();
                
                return Ok(new 
                { 
                    success = true, 
                    message = "Redis bağlantısı başarılı!", 
                    ping = pong.TotalMilliseconds + " ms",
                    server = server.EndPoint.ToString()
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    success = false, 
                    message = "Redis bağlantı hatası: " + ex.Message 
                });
            }
        }

        [HttpPost("set/{key}")]
        public async Task<IActionResult> SetValue(string key, [FromBody] object value)
        {
            try
            {
                var jsonValue = JsonSerializer.Serialize(value);
                await _database.StringSetAsync(key, jsonValue, TimeSpan.FromMinutes(5));
                
                return Ok(new 
                { 
                    success = true, 
                    message = $"Key '{key}' başarıyla kaydedildi!",
                    expiry = "5 dakika"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    success = false, 
                    message = "Redis set hatası: " + ex.Message 
                });
            }
        }

        [HttpGet("get/{key}")]
        public async Task<IActionResult> GetValue(string key)
        {
            try
            {
                var value = await _database.StringGetAsync(key);
                
                if (!value.HasValue)
                {
                    return NotFound(new 
                    { 
                        success = false, 
                        message = $"Key '{key}' bulunamadı!" 
                    });
                }

                return Ok(new 
                { 
                    success = true, 
                    key = key,
                    value = value.ToString(),
                    message = "Değer başarıyla getirildi!"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    success = false, 
                    message = "Redis get hatası: " + ex.Message 
                });
            }
        }

        [HttpDelete("delete/{key}")]
        public async Task<IActionResult> DeleteValue(string key)
        {
            try
            {
                var deleted = await _database.KeyDeleteAsync(key);
                
                return Ok(new 
                { 
                    success = true, 
                    deleted = deleted,
                    message = deleted ? $"Key '{key}' başarıyla silindi!" : $"Key '{key}' zaten mevcut değil!"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    success = false, 
                    message = "Redis delete hatası: " + ex.Message 
                });
            }
        }

        [HttpGet("keys/{pattern}")]
        public async Task<IActionResult> GetKeys(string pattern = "*")
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                var keys = server.Keys(pattern: pattern).Take(100).ToArray();
                
                return Ok(new 
                { 
                    success = true, 
                    pattern = pattern,
                    count = keys.Length,
                    keys = keys.Select(k => k.ToString()).ToArray(),
                    message = $"{keys.Length} adet key bulundu!"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    success = false, 
                    message = "Redis keys hatası: " + ex.Message 
                });
            }
        }
    }
}
