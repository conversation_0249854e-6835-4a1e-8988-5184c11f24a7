using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class ExpenseDto : IDto
    {
        public int ExpenseID { get; set; }
        public int CompanyID { get; set; }
        public string? Description { get; set; } // Nullable yapıldı
        public decimal Amount { get; set; }
        public DateTime ExpenseDate { get; set; }
        public string? ExpenseType { get; set; }
        public DateTime CreationDate { get; set; }
        // İsteğe bağlı olarak CompanyName gibi ilişkili veriler eklenebilir.
        // public string CompanyName { get; set; }
    }
}