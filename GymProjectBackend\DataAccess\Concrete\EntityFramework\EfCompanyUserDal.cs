
using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyUserDal : EfEntityRepositoryBase<CompanyUser, GymContext>, ICompanyUserDal
    {
        // Constructor injection (Scalability i�in)
        public EfCompanyUserDal(GymContext context) : base(context)
        {
        }



        public List<CompanyUserDetailDto> GetCompanyUserDetails()
        {
            // DI kullan�l�yor - Scalability optimized
            var result = from c in _context.CompanyUsers
                         join ci in _context.Cities
                         on c.CityID equals ci.CityID
                         join t in _context.Towns
                         on c.TownID equals t.TownID
                       where c.IsActive == true
                         select new CompanyUserDetailDto
                         {
                             CompanyUserId = c.CompanyUserID,
                             CompanyUserName = c.Name,
                             CityName = ci.CityName,
                             TownName = t.TownName,
                             CompanyUserPhoneNumber = c.PhoneNumber,
                             CompanyUserEmail = c.Email ?? ""
                         };
            return result.ToList();
        }
        public List<CompanyDetailDto> GetCompanyDetails()
        {
            // DI kullan�l�yor - Scalability optimized
            var result = from c in _context.CompanyUsers
                         join b in _context.UserCompanies
                        on c.CompanyUserID equals b.UserID
                         join x in _context.Cities
                         on c.CityID equals x.CityID
                         join t in _context.Towns
                         on c.TownID equals t.TownID
                         join k in _context.Companies
                         on b.CompanyId equals k.CompanyID
                         join f in _context.CompanyAdresses
                         on k.CompanyID equals f.CompanyID
                         select new CompanyDetailDto
                         {
                             CompanyUserId = c.CompanyUserID,
                             CompanyUserName = c.Name,
                             CityName = x.CityName,
                             TownName = t.TownName,
                             CompanyUserPhoneNumber = c.PhoneNumber,
                             CompanyPhoneNumber = k.PhoneNumber,
                             CompanyUserEmail = c.Email,
                             CompanyAdress = f.Adress,
                             CompanyName = k.CompanyName,
                             CompanyAdressId = f.CompanyAdressID,
                             CompanyId = k.CompanyID,
                             UserCompanyId = b.UserCompanyID,
                             IsActive = b.IsActive

                         };

            return result.ToList();
        }
        public List<CompanyDetailDto> GetCompanyUserDetailsByCityId(int cityId)
        {
            // DI kullan�l�yor - Scalability optimized
            var result = from c in _context.CompanyUsers
                         join b in _context.UserCompanies on c.CompanyUserID equals b.UserID
                         join x in _context.Cities on c.CityID equals x.CityID
                         join t in _context.Towns on c.TownID equals t.TownID
                         join k in _context.Companies on b.CompanyId equals k.CompanyID
                         join f in _context.CompanyAdresses on k.CompanyID equals f.CompanyID
                         where c.CityID == cityId
                         select new CompanyDetailDto
                         {
                             CompanyUserId = c.CompanyUserID,
                             CompanyUserName = c.Name,
                             CityName = x.CityName,
                             TownName = t.TownName,
                             CompanyUserPhoneNumber = c.PhoneNumber,
                             CompanyPhoneNumber = k.PhoneNumber,
                             CompanyUserEmail = c.Email,
                             CompanyAdress = f.Adress,
                             CompanyName = k.CompanyName
                         };

            return result.ToList();
        }

        // Yeni eklenen metodlar - OPTIMIZED VERSION
        public CompanyUserFullDetailDto GetCompanyUserFullDetails(int companyUserID)
        {
            // DI kullan�l�yor - Scalability optimized
            // ? OPTIMIZATION: LastLoginDate i�in subquery haz�rla
            var lastLoginSubquery = from device in _context.UserDevices
                                   where device.LastUsedAt.HasValue
                                   group device by device.UserId into g
                                   select new { UserId = g.Key, LastLogin = g.Max(x => x.LastUsedAt) };

            var result = from cu in _context.CompanyUsers
                         join c in _context.Cities on cu.CityID equals c.CityID
                         join t in _context.Towns on cu.TownID equals t.TownID
                         join uc in _context.UserCompanies on cu.CompanyUserID equals uc.UserID into ucGroup
                         from uc in ucGroup.DefaultIfEmpty()
                         join comp in _context.Companies on uc.CompanyId equals comp.CompanyID into compGroup
                         from comp in compGroup.DefaultIfEmpty()
                         join ca in _context.CompanyAdresses on comp.CompanyID equals ca.CompanyID into caGroup
                         from ca in caGroup.DefaultIfEmpty()
                         join u in _context.Users on cu.Email equals u.Email into userGroup
                         from u in userGroup.DefaultIfEmpty()
                         // ? OPTIMIZATION: LastLogin bilgisini tek sorguda al
                         join lastLogin in lastLoginSubquery on u.UserID equals lastLogin.UserId into loginGroup
                         from lastLogin in loginGroup.DefaultIfEmpty()
                         where cu.CompanyUserID == companyUserID
                             select new CompanyUserFullDetailDto
                             {
                                 // CompanyUser bilgileri
                                 CompanyUserID = cu.CompanyUserID,
                                 Name = cu.Name,
                                 PhoneNumber = cu.PhoneNumber,
                                 Email = cu.Email,
                                 CityID = cu.CityID,
                                 TownID = cu.TownID,
                                 CityName = c.CityName,
                                 TownName = t.TownName,
                                 IsActive = cu.IsActive,
                                 CreationDate = cu.CreationDate,
                                 UpdatedDate = cu.UpdatedDate,

                                 // User bilgileri
                                 UserID = u != null ? u.UserID : (int?)null,
                                 FirstName = u != null ? u.FirstName : null,
                                 LastName = u != null ? u.LastName : null,
                                 UserIsActive = u != null ? u.IsActive : false,
                                 // ? OPTIMIZATION: Art�k tek sorguda geliyor!
                                 LastLoginDate = lastLogin != null ? lastLogin.LastLogin : (DateTime?)null,
                                 RequirePasswordChange = u != null ? u.RequirePasswordChange : false,

                                 // Company bilgileri
                                 CompanyID = comp != null ? comp.CompanyID : (int?)null,
                                 CompanyName = comp != null ? comp.CompanyName : null,
                                 CompanyPhone = comp != null ? comp.PhoneNumber : null,
                                 CompanyAddress = ca != null ? ca.Adress : null,
                                 CompanyCityID = ca != null ? ca.CityID : (int?)null,
                                 CompanyTownID = ca != null ? ca.TownID : (int?)null,
                                 CompanyIsActive = comp != null ? comp.IsActive : (bool?)null,

                                 // �li�ki ID'leri
                                 CompanyAddressId = ca != null ? ca.CompanyAdressID : (int?)null,
                                 UserCompanyId = uc != null ? uc.UserCompanyID : (int?)null,

                                 // �statistik bilgileri hesaplanacak
                                 TotalMembers = comp != null ? _context.Members
                                     .Where(m => m.CompanyID == comp.CompanyID && m.IsActive == true)
                                     .Count() : 0,
                                 ActiveMembers = 0, // Kullan�lmayacak
                                 MonthlyRevenue = 0 // Kullan�lmayacak
                             };

            return result.FirstOrDefault();
        }

        public PaginatedCompanyUserDto GetCompanyUsersPaginated(int pageNumber, int pageSize, string searchTerm = "")
        {
            // DI kullan�l�yor - Scalability optimized
            var query = from cu in _context.CompanyUsers
                       join c in _context.Cities on cu.CityID equals c.CityID
                       join t in _context.Towns on cu.TownID equals t.TownID
                       join uc in _context.UserCompanies on cu.CompanyUserID equals uc.UserID into ucGroup
                       from uc in ucGroup.DefaultIfEmpty()
                       join comp in _context.Companies on uc.CompanyId equals comp.CompanyID into compGroup
                       from comp in compGroup.DefaultIfEmpty()
                       join ca in _context.CompanyAdresses on comp.CompanyID equals ca.CompanyID into caGroup
                       from ca in caGroup.DefaultIfEmpty()
                       join u in _context.Users on cu.Email equals u.Email into userGroup
                       from u in userGroup.DefaultIfEmpty()
                       where cu.IsActive == true
                       select new CompanyUserFullDetailDto
                       {
                           CompanyUserID = cu.CompanyUserID,
                           Name = cu.Name,
                           PhoneNumber = cu.PhoneNumber,
                           Email = cu.Email,
                           CityID = cu.CityID,
                           TownID = cu.TownID,
                           CityName = c.CityName,
                           TownName = t.TownName,
                           IsActive = cu.IsActive,
                           CreationDate = cu.CreationDate,
                           UpdatedDate = cu.UpdatedDate,
                           UserID = u != null ? u.UserID : (int?)null,
                           FirstName = u != null ? u.FirstName : null,
                           LastName = u != null ? u.LastName : null,
                           UserIsActive = u != null ? u.IsActive : false,
                           LastLoginDate = u != null ? _context.UserDevices
                               .Where(ud => ud.UserId == u.UserID && ud.LastUsedAt.HasValue)
                               .OrderByDescending(ud => ud.LastUsedAt)
                               .Select(ud => ud.LastUsedAt)
                               .FirstOrDefault() : (DateTime?)null,
                           RequirePasswordChange = u != null ? u.RequirePasswordChange : false,
                           CompanyID = comp != null ? comp.CompanyID : (int?)null,
                           CompanyName = comp != null ? comp.CompanyName : null,
                           CompanyPhone = comp != null ? comp.PhoneNumber : null,
                           CompanyAddress = ca != null ? ca.Adress : null,
                           CompanyCityID = ca != null ? ca.CityID : (int?)null,
                           CompanyTownID = ca != null ? ca.TownID : (int?)null,
                           CompanyIsActive = comp != null ? comp.IsActive : (bool?)null,

                           // �li�ki ID'leri
                           CompanyAddressId = ca != null ? ca.CompanyAdressID : (int?)null,
                           UserCompanyId = uc != null ? uc.UserCompanyID : (int?)null,
                           TotalMembers = comp != null ? _context.Members
                               .Where(m => m.CompanyID == comp.CompanyID && m.IsActive == true)
                               .Count() : 0,
                           ActiveMembers = 0, // Kullan�lmayacak
                           MonthlyRevenue = 0 // Kullan�lmayacak
                       };

            // Arama filtresi uygula
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                searchTerm = searchTerm.ToLower();
                query = query.Where(x =>
                    (x.Name != null && x.Name.ToLower().Contains(searchTerm)) ||
                    (x.Email != null && x.Email.ToLower().Contains(searchTerm)) ||
                    (x.PhoneNumber != null && x.PhoneNumber.Contains(searchTerm)) ||
                    (x.CityName != null && x.CityName.ToLower().Contains(searchTerm)) ||
                    (x.TownName != null && x.TownName.ToLower().Contains(searchTerm)) ||
                    (x.CompanyName != null && x.CompanyName.ToLower().Contains(searchTerm))
                );
            }

            // Toplam kay�t say�s�n� al
            int totalCount = query.Count();

            // Sayfalama uygula
            var data = query
                .OrderBy(x => x.Name ?? "")
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            return new PaginatedCompanyUserDto(data, totalCount, pageNumber, pageSize);
        }

        // Salon soft delete (temel tablolar + User)
        public IResult SoftDeleteCompanyUserBasic(int companyUserID)
        {
            // DI kullan�l�yor - Scalability optimized
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    var deleteDate = DateTime.Now;

                    // 1. CompanyUser soft delete
                    var companyUser = _context.CompanyUsers.FirstOrDefault(cu => cu.CompanyUserID == companyUserID);
                    if (companyUser == null)
                    {
                        return new ErrorResult("Salon sahibi bulunamad�");
                    }

                    companyUser.IsActive = false;
                    companyUser.DeletedDate = deleteDate;

                    // 2. UserCompany soft delete ve CompanyId'leri topla
                    var userCompanies = _context.UserCompanies.Where(uc => uc.UserID == companyUserID).ToList();
                    var companyIds = new List<int>();

                    foreach (var userCompany in userCompanies)
                    {
                        userCompany.IsActive = false;
                        userCompany.DeletedDate = deleteDate;
                        companyIds.Add(userCompany.CompanyId);
                    }

                    // 3. Company soft delete
                    var companies = _context.Companies.Where(c => companyIds.Contains(c.CompanyID)).ToList();
                    foreach (var company in companies)
                    {
                        company.IsActive = false;
                        company.DeletedDate = deleteDate;
                    }

                    // 4. CompanyAddress soft delete
                    var companyAddresses = _context.CompanyAdresses.Where(ca => companyIds.Contains(ca.CompanyID)).ToList();
                    foreach (var address in companyAddresses)
                    {
                        address.IsActive = false;
                        address.DeletedDate = deleteDate;
                    }

                    // 5. User soft delete (CompanyUser ile ili�kili User'� bul)
                    var users = _context.Users.Where(u => userCompanies.Select(uc => uc.UserID).Contains(u.UserID)).ToList();
                    foreach (var user in users)
                    {
                        user.IsActive = false;
                        user.DeletedDate = deleteDate;
                    }

                    _context.SaveChanges();
                    transaction.Commit();

                    return new SuccessResult("Salon ba�ar�yla silindi");
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    return new ErrorResult($"Salon silinirken hata olu�tu: {ex.Message}");
                }
            }
        }

        // Silinen salonlar� listeleme
        public List<DeletedCompanyUserDto> GetDeletedCompanyUsers()
        {
            // DI kullan�l�yor - Scalability optimized
            var result = from cu in _context.CompanyUsers
                         join c in _context.Cities on cu.CityID equals c.CityID
                         join t in _context.Towns on cu.TownID equals t.TownID
                         join uc in _context.UserCompanies on cu.CompanyUserID equals uc.UserID into userCompanies
                         from uc in userCompanies.DefaultIfEmpty()
                         join comp in _context.Companies on uc.CompanyId equals comp.CompanyID into companies
                         from comp in companies.DefaultIfEmpty()
                         join ca in _context.CompanyAdresses on comp.CompanyID equals ca.CompanyID into addresses
                         from ca in addresses.DefaultIfEmpty()
                         join u in _context.Users on uc.UserID equals u.UserID into users
                         from u in users.DefaultIfEmpty()
                         where cu.IsActive == false && cu.DeletedDate.HasValue
                         select new DeletedCompanyUserDto
                         {
                             CompanyUserID = cu.CompanyUserID,
                             Name = cu.Name,
                             Email = cu.Email,
                             PhoneNumber = cu.PhoneNumber,
                             CityName = c.CityName,
                             TownName = t.TownName,
                             DeletedDate = cu.DeletedDate,
                             CompanyID = comp != null ? comp.CompanyID : (int?)null,
                             CompanyName = comp != null ? comp.CompanyName : null,
                             CompanyPhone = comp != null ? comp.PhoneNumber : null,
                             CompanyAddress = ca != null ? ca.Adress : null,
                             UserID = u != null ? u.UserID : (int?)null,
                             TotalMembers = comp != null ? _context.Members
                                 .Where(m => m.CompanyID == comp.CompanyID)
                                 .Count() : 0,
                             CanRestore = true
                         };

            return result.ToList();
        }

        // Salon geri y�kleme
        public IResult RestoreCompanyUserBasic(int companyUserID)
        {
            // DI kullan�l�yor - Scalability optimized
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    // 1. CompanyUser restore
                    var companyUser = _context.CompanyUsers.FirstOrDefault(cu => cu.CompanyUserID == companyUserID);
                    if (companyUser == null)
                    {
                        return new ErrorResult("Salon sahibi bulunamad�");
                    }

                    companyUser.IsActive = true;
                    companyUser.DeletedDate = null;
                    companyUser.UpdatedDate = DateTime.Now;

                    // 2. UserCompany restore
                    var userCompanies = _context.UserCompanies.Where(uc => uc.UserID == companyUserID).ToList();
                    foreach (var userCompany in userCompanies)
                    {
                        userCompany.IsActive = true;
                        userCompany.DeletedDate = null;
                        userCompany.UpdatedDate = DateTime.Now;
                    }

                    // 3. Company restore
                    var companies = _context.Companies.Where(c => userCompanies.Select(uc => uc.CompanyId).Contains(c.CompanyID)).ToList();
                    foreach (var company in companies)
                    {
                        company.IsActive = true;
                        company.DeletedDate = null;
                        company.UpdatedDate = DateTime.Now;
                    }

                    // 4. CompanyAddress restore
                    var companyAddresses = _context.CompanyAdresses.Where(ca => companies.Select(c => c.CompanyID).Contains(ca.CompanyID)).ToList();
                    foreach (var address in companyAddresses)
                    {
                        address.IsActive = true;
                        address.DeletedDate = null;
                        address.UpdatedDate = DateTime.Now;
                    }

                    // 5. User restore
                    var users = _context.Users.Where(u => userCompanies.Select(uc => uc.UserID).Contains(u.UserID)).ToList();
                    foreach (var user in users)
                    {
                        user.IsActive = true;
                        user.DeletedDate = null;
                        user.UpdatedDate = DateTime.Now;
                    }

                    _context.SaveChanges();
                    transaction.Commit();

                    return new SuccessResult("Salon ba�ar�yla geri y�klendi");
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    return new ErrorResult($"Salon geri y�klenirken hata olu�tu: {ex.Message}");
                }
            }
        }

        // CompanyUser full update i�lemi
        public IResult UpdateCompanyUserFull(CompanyUserFullUpdateDto updateDto)
        {
            // DI kullan�l�yor - Scalability optimized
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    // 1. Mevcut CompanyUser kayd�n� al
                    var existingCompanyUser = _context.CompanyUsers.FirstOrDefault(cu => cu.CompanyUserID == updateDto.CompanyUserID);
                    if (existingCompanyUser == null)
                    {
                        return new ErrorResult("�irket kullan�c�s� bulunamad�");
                    }

                    string oldEmail = existingCompanyUser.Email;
                    string oldName = existingCompanyUser.Name;

                    // 2. CompanyUser tablosunu g�ncelle
                    existingCompanyUser.Name = updateDto.Name;
                    existingCompanyUser.PhoneNumber = updateDto.PhoneNumber;
                    existingCompanyUser.Email = updateDto.Email;
                    existingCompanyUser.CityID = updateDto.CityID;
                    existingCompanyUser.TownID = updateDto.TownID;
                    existingCompanyUser.IsActive = updateDto.IsActive;
                    existingCompanyUser.UpdatedDate = DateTime.Now;

                    // 3. Email veya isim de�i�mi�se User tablosunu g�ncelle
                    bool emailChanged = oldEmail != updateDto.Email;
                    bool nameChanged = oldName != updateDto.Name;

                    if (emailChanged || nameChanged)
                    {
                        var existingUser = _context.Users.FirstOrDefault(u => u.Email == oldEmail);
                        if (existingUser != null)
                        {
                            // Email g�ncelle
                            if (emailChanged)
                            {
                                existingUser.Email = updateDto.Email;
                            }

                            // �sim g�ncelle
                            if (nameChanged)
                            {
                                var (firstName, lastName) = ParseFullName(updateDto.Name);
                                existingUser.FirstName = firstName;
                                existingUser.LastName = lastName;
                            }

                            existingUser.UpdatedDate = DateTime.Now;
                        }
                    }

                    // 4. Company bilgileri g�ncellenmi�se Company tablosunu g�ncelle
                    if (updateDto.CompanyDataChanged && !string.IsNullOrEmpty(updateDto.CompanyName))
                    {
                        var userCompany = _context.UserCompanies
                            .FirstOrDefault(uc => uc.UserID == updateDto.CompanyUserID && uc.IsActive == true);

                        if (userCompany != null)
                        {
                            // Company tablosunu g�ncelle
                            var existingCompany = _context.Companies.FirstOrDefault(c => c.CompanyID == userCompany.CompanyId);
                            if (existingCompany != null)
                            {
                                existingCompany.CompanyName = updateDto.CompanyName;
                                existingCompany.PhoneNumber = updateDto.CompanyPhone;
                                existingCompany.UpdatedDate = DateTime.Now;
                            }

                            // CompanyAddress tablosunu g�ncelle
                            var existingAddress = _context.CompanyAdresses
                                .FirstOrDefault(ca => ca.CompanyID == userCompany.CompanyId && ca.IsActive == true);

                            if (existingAddress != null)
                            {
                                existingAddress.Adress = updateDto.CompanyAddress;
                                existingAddress.CityID = updateDto.CompanyCityID ?? existingAddress.CityID;
                                existingAddress.TownID = updateDto.CompanyTownID ?? existingAddress.TownID;
                                existingAddress.UpdatedDate = DateTime.Now;
                            }
                        }
                    }

                    _context.SaveChanges();
                    transaction.Commit();

                    return new SuccessResult("�irket kullan�c�s� ve �irket bilgileri ba�ar�yla g�ncellendi");
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    return new ErrorResult($"G�ncelleme s�ras�nda hata olu�tu: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// �smi FirstName ve LastName olarak ay�r�r
        /// Son kelimeyi LastName, geri kalan�n� FirstName olarak al�r
        /// </summary>
        private (string FirstName, string LastName) ParseFullName(string fullName)
        {
            if (string.IsNullOrWhiteSpace(fullName))
            {
                return ("", "");
            }

            fullName = fullName.Trim();
            fullName = System.Text.RegularExpressions.Regex.Replace(fullName, @"\s+", " ");

            string[] nameParts = fullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);

            if (nameParts.Length == 0)
            {
                return ("", "");
            }
            else if (nameParts.Length == 1)
            {
                return (nameParts[0], "");
            }
            else
            {
                string lastName = nameParts[nameParts.Length - 1];
                string firstName = string.Join(" ", nameParts.Take(nameParts.Length - 1));
                return (firstName, lastName);
            }
        }
    }
}