﻿using Core.Entities;
using Microsoft.Extensions.Primitives;
using Microsoft.IdentityModel.Protocols;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class CompanyUserAddDto:IDto
    {
        public string Name { get; set; }
        public string CompanyUserPhoneNumber{ get; set; }
        public int CityId { get; set; }
        public int TownId { get; set; }
        public string EMail{ get; set; }
        public string Adress { get; set; }
        public string CompanyName { get; set; }
        public string CompanyPhoneNumber { get; set; }
    }
}
