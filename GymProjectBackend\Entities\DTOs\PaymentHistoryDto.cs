﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class PaymentHistoryDto:IDto
    {
        public int PaymentID { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; }
        public decimal PaymentAmount { get; set; }
        public string MembershipType { get; set; }
        public string Branch{ get; set; }
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public bool? IsActive { get; set; }
        public decimal CurrentRemainingAmount { get; set; }

    }
}
