﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class GetActiveMemberDto:IDto
    {
        public int MemberID { get; set; }
        public string Name { get; set; }
        public byte Gender { get; set; }
        public string PhoneNumber { get; set; }
        public string? Adress { get; set; }
        public DateOnly? BirthDate { get; set; }
        public string? Email { get; set; }
        public bool? IsActive { get; set; }
        public string? ScanNumber { get; set; }
        public decimal Balance { get; set; }
    }
}
