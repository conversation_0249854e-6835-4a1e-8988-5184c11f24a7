﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;

namespace DataAccess.Abstract
{
    public interface ILicenseTransactionDal : IEntityRepository<LicenseTransaction>
    {
        IDataResult<List<LicenseTransaction>> GetAllFiltered(int? userID, string startDate, string endDate, int page, int pageSize);
        IResult AddLicenseTransaction(LicenseTransaction licenseTransaction);
        IResult SoftDeleteLicenseTransaction(int id);
        IDataResult<List<LicenseTransaction>> GetAllOrderedByDate();
        IDataResult<object> GetTotalAmountsByPaymentMethod();
        IDataResult<object> GetMonthlyRevenueByYear(int year);
    }

}
