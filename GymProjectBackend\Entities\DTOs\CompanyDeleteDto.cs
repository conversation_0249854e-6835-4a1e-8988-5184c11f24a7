﻿ using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class CompanyDeleteDto : IDto
    {
        public int UserCompanyID { get; set; }
        public int CompanyUserID { get; set; }
        public int CompanyAdressID { get; set; }
        public int CompanyID { get; set; }

    }
}
