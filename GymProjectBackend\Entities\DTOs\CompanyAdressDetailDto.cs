﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class CompanyAdressDetailDto : IDto
    {
        public int CompanyAdressID { get; set; }
        public string CompanyName { get; set; }
        public string CityName { get; set; }
        public string TownName { get; set; }
        public string Adress { get; set; }
        public bool? isActive { get; set; }
    }
}
