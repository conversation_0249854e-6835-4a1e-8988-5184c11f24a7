using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ExerciseCategoriesController : ControllerBase
    {
        IExerciseCategoryService _exerciseCategoryService;

        public ExerciseCategoriesController(IExerciseCategoryService exerciseCategoryService)
        {
            _exerciseCategoryService = exerciseCategoryService;
        }

        /// <summary>
        /// Tüm egzersiz kategorilerini getirir
        /// </summary>
        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _exerciseCategoryService.GetAllCategories();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Aktif egzersiz kategorilerini getirir
        /// </summary>
        [HttpGet("getactive")]
        public IActionResult GetActive()
        {
            var result = _exerciseCategoryService.GetActiveCategories();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// ID'ye göre egzersiz kategorisi getirir
        /// </summary>
        [HttpGet("getbyid/{id}")]
        public IActionResult GetById(int id)
        {
            var result = _exerciseCategoryService.GetById(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Yeni egzersiz kategorisi ekler (Sadece owner)
        /// </summary>
        [HttpPost("add")]
        public IActionResult Add([FromBody] ExerciseCategoryAddDto categoryAddDto)
        {
            var result = _exerciseCategoryService.Add(categoryAddDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Egzersiz kategorisini günceller (Sadece owner)
        /// </summary>
        [HttpPost("update")]
        public IActionResult Update([FromBody] ExerciseCategoryUpdateDto categoryUpdateDto)
        {
            var result = _exerciseCategoryService.Update(categoryUpdateDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Egzersiz kategorisini siler (Sadece owner)
        /// </summary>
        [HttpDelete("delete/{id}")]
        public IActionResult Delete(int id)
        {
            var result = _exerciseCategoryService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}
