﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MemberRemainingDayDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string ScanNumber { get; set; }
        public string PhoneNumber { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public string Message { get; set; }
        public string Branch { get; set; }
    }

    public class MemberDetailDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string PhoneNumber { get; set; }
        public string Message { get; set; }
        public List<MembershipDetailDto> Memberships { get; set; }
    }

    public class MembershipDetailDto : IDto
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public string Branch { get; set; }
    }
}
