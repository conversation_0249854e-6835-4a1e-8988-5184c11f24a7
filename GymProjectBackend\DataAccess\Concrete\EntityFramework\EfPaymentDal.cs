﻿using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfPaymentDal : EfCompanyEntityRepositoryBase<Payment, GymContext>, IPaymentDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfPaymentDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        public PaginatedResult<PaymentHistoryDto> GetPaymentHistoryPaginated(PaymentPagingParameters parameters)
        {
            // DI kullanılıyor - Scalability optimized
            int companyId = _companyContext.GetCompanyId();

            var paymentsQuery = from p in _context.Payments
                                    join ms in _context.Memberships on p.MemberShipID equals ms.MembershipID
                                    join m in _context.Members on ms.MemberID equals m.MemberID
                                    join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                    where p.IsActive == true
                                    && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                    && p.CompanyID == companyId
                                    && ms.CompanyID == companyId
                                    && m.CompanyID == companyId
                                    && mt.CompanyID == companyId
                                    select new PaymentHistoryDto
                                    {
                                        PaymentID = p.PaymentID,
                                        PaymentDate = p.PaymentDate,
                                        PaymentMethod = p.PaymentMethod,
                                        PaymentAmount = p.PaymentAmount,
                                        MembershipType = mt.TypeName,
                                        Branch = mt.Branch,
                                        Name = m.Name,
                                        PhoneNumber = m.PhoneNumber,
                                        IsActive = p.IsActive
                                    };

                var debtPaymentsQuery = from dp in _context.DebtPayments
                                        join rd in _context.RemainingDebts on dp.RemainingDebtID equals rd.RemainingDebtID
                                        join p in _context.Payments on rd.PaymentID equals p.PaymentID
                                        join ms in _context.Memberships on p.MemberShipID equals ms.MembershipID
                                        join m in _context.Members on ms.MemberID equals m.MemberID
                                        join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                        where dp.IsActive == true
                                        && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                        && dp.CompanyID == companyId
                                        && rd.CompanyID == companyId
                                        && p.CompanyID == companyId
                                        && ms.CompanyID == companyId
                                        && m.CompanyID == companyId
                                        && mt.CompanyID == companyId
                                        select new PaymentHistoryDto
                                        {
                                            PaymentID = dp.DebtPaymentID,
                                            PaymentDate = dp.PaymentDate,
                                            PaymentMethod = dp.PaymentMethod + " (Borç Ödemesi)",
                                            PaymentAmount = dp.PaidAmount,
                                            MembershipType = mt.TypeName,
                                            Branch = mt.Branch,
                                            Name = m.Name,
                                            PhoneNumber = m.PhoneNumber,
                                            IsActive = dp.IsActive
                                        };

                var combinedQuery = paymentsQuery.Union(debtPaymentsQuery);

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    var searchText = parameters.SearchText.ToLower();
                    combinedQuery = combinedQuery.Where(x =>
                        (x.Name != null && x.Name.ToLower().Contains(searchText)) ||
                        (x.PhoneNumber != null && x.PhoneNumber.Contains(searchText)));
                }

                if (parameters.StartDate.HasValue)
                {
                    combinedQuery = combinedQuery.Where(x => x.PaymentDate >= parameters.StartDate.Value.Date);
                }

                if (parameters.EndDate.HasValue)
                {
                    var endDateEndOfDay = parameters.EndDate.Value.Date.AddDays(1).AddTicks(-1);
                    combinedQuery = combinedQuery.Where(x => x.PaymentDate <= endDateEndOfDay);
                }

                if (!string.IsNullOrWhiteSpace(parameters.PaymentMethod))
                {
                    combinedQuery = combinedQuery.Where(x => x.PaymentMethod == parameters.PaymentMethod);
                }

                combinedQuery = combinedQuery.OrderByDescending(x => x.PaymentDate);

                var totalCount = combinedQuery.Count();

                var items = combinedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<PaymentHistoryDto>(
                    items,
                    parameters.PageNumber,
                    parameters.PageSize,
                    totalCount
                );
        }

        public PaymentTotals GetPaymentTotals(PaymentPagingParameters parameters)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();

                var paymentsQuery = from p in _context.Payments
                                        join ms in _context.Memberships on p.MemberShipID equals ms.MembershipID
                                        join m in _context.Members on ms.MemberID equals m.MemberID
                                        where p.IsActive == true
                                        && p.OriginalPaymentMethod != "Borç"
                                        && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                        && p.CompanyID == companyId
                                        && ms.CompanyID == companyId
                                        && m.CompanyID == companyId
                                        select new
                                        {
                                            p.OriginalPaymentMethod,
                                            p.PaymentAmount,
                                            p.PaymentDate,
                                            m.Name,
                                            m.PhoneNumber
                                        };

                    var debtPaymentsQuery = from dp in _context.DebtPayments
                                            join rd in _context.RemainingDebts on dp.RemainingDebtID equals rd.RemainingDebtID
                                            join p in _context.Payments on rd.PaymentID equals p.PaymentID
                                            join ms in _context.Memberships on p.MemberShipID equals ms.MembershipID
                                            join m in _context.Members on ms.MemberID equals m.MemberID
                                            where dp.IsActive == true
                                            && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                            && dp.CompanyID == companyId
                                            && rd.CompanyID == companyId
                                            && p.CompanyID == companyId
                                            && ms.CompanyID == companyId
                                            && m.CompanyID == companyId
                                            select new
                                            {
                                                PaymentMethod = dp.PaymentMethod,
                                                PaymentAmount = dp.PaidAmount,
                                                dp.PaymentDate,
                                                m.Name,
                                                m.PhoneNumber
                                            };

                    if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                    {
                        var searchText = parameters.SearchText.ToLower();
                        paymentsQuery = paymentsQuery.Where(x =>
                            (x.Name != null && x.Name.ToLower().Contains(searchText)) ||
                            (x.PhoneNumber != null && x.PhoneNumber.Contains(searchText)));

                        debtPaymentsQuery = debtPaymentsQuery.Where(x =>
                            (x.Name != null && x.Name.ToLower().Contains(searchText)) ||
                            (x.PhoneNumber != null && x.PhoneNumber.Contains(searchText)));
                    }

                    if (parameters.StartDate.HasValue)
                    {
                        var startDate = parameters.StartDate.Value.Date;
                        var endDateExclusive = (parameters.EndDate?.Date ?? DateTime.Today).AddDays(1);

                        paymentsQuery = paymentsQuery.Where(x =>
                            x.PaymentDate >= startDate && x.PaymentDate < endDateExclusive);

                        debtPaymentsQuery = debtPaymentsQuery.Where(x =>
                            x.PaymentDate >= startDate && x.PaymentDate < endDateExclusive);
                    }
                    else if (!parameters.EndDate.HasValue && string.IsNullOrWhiteSpace(parameters.SearchText))
                    {
                        var today = DateTime.Today;
                        var firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
                        var nextMonthStartDateForTotals = firstDayOfMonth.AddMonths(1);

                        paymentsQuery = paymentsQuery.Where(x =>
                            x.PaymentDate >= firstDayOfMonth && x.PaymentDate < nextMonthStartDateForTotals);

                        debtPaymentsQuery = debtPaymentsQuery.Where(x =>
                            x.PaymentDate >= firstDayOfMonth && x.PaymentDate < nextMonthStartDateForTotals);
                    }

                    var normalPayments = paymentsQuery
                        .GroupBy(x => 1)
                        .Select(g => new
                        {
                            Cash = g.Where(x => x.OriginalPaymentMethod == "Nakit")
                                .Sum(x => x.PaymentAmount),
                            CreditCard = g.Where(x => x.OriginalPaymentMethod == "Kredi Kartı")
                                .Sum(x => x.PaymentAmount),
                            Transfer = g.Where(x => x.OriginalPaymentMethod == "Havale - EFT")
                                .Sum(x => x.PaymentAmount)
                        })
                        .FirstOrDefault() ?? new { Cash = 0m, CreditCard = 0m, Transfer = 0m };

                    var debtPayments = debtPaymentsQuery
                        .GroupBy(x => 1)
                        .Select(g => new
                        {
                            Cash = g.Where(x => x.PaymentMethod == "Nakit")
                                .Sum(x => x.PaymentAmount),
                            CreditCard = g.Where(x => x.PaymentMethod == "Kredi Kartı")
                                .Sum(x => x.PaymentAmount),
                            Transfer = g.Where(x => x.PaymentMethod == "Havale - EFT")
                                .Sum(x => x.PaymentAmount)
                        })
                        .FirstOrDefault() ?? new { Cash = 0m, CreditCard = 0m, Transfer = 0m };

                    var remainingDebtsQuery = from rd in _context.RemainingDebts
                                              where rd.IsActive == true
                                              && rd.CompanyID == companyId
                                              select rd.RemainingAmount;

                    var totalRemainingDebt = remainingDebtsQuery.Sum();

                    return new PaymentTotals
                    {
                        Cash = normalPayments.Cash + debtPayments.Cash,
                        CreditCard = normalPayments.CreditCard + debtPayments.CreditCard,
                        Transfer = normalPayments.Transfer + debtPayments.Transfer,
                        Debt = totalRemainingDebt
                    };
                }
                catch (Exception ex)
                {
                    // Loglama eklenebilir
                    return new PaymentTotals { Cash = 0, CreditCard = 0, Transfer = 0, Debt = 0 };
                }
        }

        public List<PaymentHistoryDto> GetPaymentHistory()
        {
            int companyId = _companyContext.GetCompanyId();

            var result = from a in _context.Memberships
                         join b in _context.Payments
                         on a.MembershipID equals b.MemberShipID
                         join c in _context.MembershipTypes
                         on a.MembershipTypeID equals c.MembershipTypeID
                         join d in _context.Members
                         on a.MemberID equals d.MemberID
                         join rd in _context.RemainingDebts
                         on b.PaymentID equals rd.PaymentID into remainingDebts
                         from rd in remainingDebts.DefaultIfEmpty()
                         where b.IsActive == true
                         && d.IsActive == true // Member'ın aktif olması kontrolü eklendi
                         && a.IsActive == true // Membership'ın aktif olması kontrolü eklendi
                         && b.CompanyID == companyId
                         && a.CompanyID == companyId
                         && c.CompanyID == companyId
                         && d.CompanyID == companyId
                         && (rd == null || (rd.CompanyID == companyId && rd.IsActive == true))
                         select new PaymentHistoryDto
                         {
                             PaymentID = b.PaymentID,
                             PaymentDate = b.PaymentDate,
                             PaymentAmount = b.PaymentAmount,
                             PaymentMethod = b.PaymentMethod,
                             MembershipType = c.TypeName,
                             Branch = c.Branch,
                             Name = d.Name,
                             IsActive = b.IsActive,
                             PhoneNumber = d.PhoneNumber,
                             CurrentRemainingAmount = rd != null ? rd.RemainingAmount : 0
                         };
            return result.ToList();
        }

        public List<PaymentHistoryDto> GetDebtorMembers()
        {
            int companyId = _companyContext.GetCompanyId();

            var result = from a in _context.Memberships
                         join b in _context.Payments
                         on a.MembershipID equals b.MemberShipID
                         join c in _context.MembershipTypes
                         on a.MembershipTypeID equals c.MembershipTypeID
                         join d in _context.Members
                         on a.MemberID equals d.MemberID
                         where b.IsActive == true && b.PaymentStatus == "Pending"
                         && d.IsActive == true // Member'ın aktif olması kontrolü eklendi
                         && a.IsActive == true // Membership'ın aktif olması kontrolü eklendi
                         && b.CompanyID == companyId
                         && a.CompanyID == companyId
                         && c.CompanyID == companyId
                         && d.CompanyID == companyId
                             select new PaymentHistoryDto
                             {
                                 PaymentID = b.PaymentID,
                                 PaymentDate = b.PaymentDate,
                                 PaymentAmount = b.PaymentAmount,
                                 PaymentMethod = b.PaymentMethod,
                                 MembershipType = c.TypeName,
                                 Branch = c.Branch,
                                 Name = d.Name,
                                 IsActive = b.IsActive,
                                 PhoneNumber = d.PhoneNumber
                             };
                return result.ToList();
        }
        public bool UpdatePaymentStatus(int paymentId, string paymentMethod)
        {
            int companyId = _companyContext.GetCompanyId();

            var payment = _context.Payments.FirstOrDefault(p => p.PaymentID == paymentId && p.CompanyID == companyId);
            if (payment != null)
            {
                payment.PaymentStatus = "Completed";
                payment.PaymentMethod = paymentMethod;
                _context.SaveChanges();
                return true;
            }
            return false;
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Soft delete işlemi DAL katmanında
        /// </summary>
        public IResult SoftDeletePayment(int paymentId, int companyId)
        {
            try
            {
                // DI kullanılıyor - Scalability optimized
                var payment = _context.Payments.FirstOrDefault(p => p.PaymentID == paymentId && p.CompanyID == companyId);
                if (payment == null)
                {
                    return new ErrorResult("Ödeme bulunamadı veya erişim yetkiniz yok.");
                }

                payment.IsActive = false;
                payment.DeletedDate = DateTime.Now;
                _context.Payments.Update(payment);
                _context.SaveChanges();

                return new SuccessResult("Ödeme başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Ödeme silinirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Update işlemi business logic ile DAL katmanında
        /// </summary>
        public IResult UpdatePaymentWithBusinessLogic(Payment payment, int companyId)
        {
            try
            {
                // DI kullanılıyor - Scalability optimized
                var existingPayment = _context.Payments.FirstOrDefault(p => p.PaymentID == payment.PaymentID && p.CompanyID == companyId);
                if (existingPayment == null)
                {
                    return new ErrorResult("Ödeme bulunamadı veya erişim yetkiniz yok.");
                }

                // Güvenlik için CompanyID'yi tekrar ata ve CreationDate'i koru
                payment.CompanyID = companyId;
                payment.CreationDate = existingPayment.CreationDate;
                payment.UpdatedDate = DateTime.Now;

                _context.Payments.Update(payment);
                _context.SaveChanges();

                return new SuccessResult("Ödeme başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Ödeme güncellenirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Payment status update işlemi business logic ile DAL katmanında
        /// </summary>
        public IResult UpdatePaymentStatusWithBusinessLogic(int paymentId, string paymentMethod)
        {
            try
            {
                // DI kullanılıyor - Scalability optimized
                var payment = _context.Payments.FirstOrDefault(p => p.PaymentID == paymentId);
                if (payment == null)
                {
                    return new ErrorResult("Ödeme bulunamadı.");
                }

                payment.PaymentStatus = "Completed";
                payment.PaymentMethod = paymentMethod;
                payment.UpdatedDate = DateTime.Now;
                _context.Payments.Update(payment);
                _context.SaveChanges();

                return new SuccessResult("Ödeme durumu başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Ödeme durumu güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public MonthlyRevenueDto GetMonthlyRevenue(int year)
        {
            try
            {
                // DI kullanılıyor - Scalability optimized
                int companyId = _companyContext.GetCompanyId();

                string[] turkishMonths = { "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" };

                var result = new MonthlyRevenueDto
                {
                    Year = year,
                    Months = turkishMonths.ToList()
                };

                var monthlyRevenues = new List<decimal>();

                for (int month = 1; month <= 12; month++)
                {
                    var startDate = new DateTime(year, month, 1);
                    var nextMonthStartDate = startDate.AddMonths(1);

                    var normalPayments = _context.Payments
                        .Where(p => p.IsActive == true
                            && p.CompanyID == companyId
                            && p.OriginalPaymentMethod != "Borç"
                            && p.PaymentDate >= startDate
                            && p.PaymentDate < nextMonthStartDate)
                        .Sum(p => (decimal?)p.PaymentAmount) ?? 0m;

                    var debtPayments = _context.DebtPayments
                        .Where(dp => dp.IsActive == true
                            && dp.CompanyID == companyId
                            && dp.PaymentDate >= startDate
                            && dp.PaymentDate < nextMonthStartDate)
                        .Sum(dp => (decimal?)dp.PaidAmount) ?? 0m;

                    var totalRevenue = normalPayments + debtPayments;
                    monthlyRevenues.Add(totalRevenue);
                }

                result.MonthlyRevenue = monthlyRevenues;
                return result;
            }
            catch (Exception ex)
            {
                // Loglama eklenebilir
                return new MonthlyRevenueDto
                {
                    Year = year,
                    Months = new string[] { "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" }.ToList(),
                    MonthlyRevenue = Enumerable.Repeat(0m, 12).ToList()
                };
            }
        }

        // Excel export için eklendi
        public List<PaymentHistoryDto> GetAllCombinedPaymentHistory(PaymentPagingParameters parameters)
        {
            // DI kullanılıyor - Scalability optimized
            int companyId = _companyContext.GetCompanyId();

            var paymentsQuery = from p in _context.Payments
                                join ms in _context.Memberships on p.MemberShipID equals ms.MembershipID
                                join m in _context.Members on ms.MemberID equals m.MemberID
                                join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                where p.IsActive == true
                                && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                && p.CompanyID == companyId
                                && ms.CompanyID == companyId
                                && m.CompanyID == companyId
                                && mt.CompanyID == companyId
                                select new PaymentHistoryDto
                                {
                                    PaymentID = p.PaymentID,
                                    PaymentDate = p.PaymentDate,
                                    PaymentMethod = p.PaymentMethod,
                                    PaymentAmount = p.PaymentAmount,
                                    MembershipType = mt.TypeName,
                                    Branch = mt.Branch,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    IsActive = p.IsActive
                                };

            var debtPaymentsQuery = from dp in _context.DebtPayments
                                    join rd in _context.RemainingDebts on dp.RemainingDebtID equals rd.RemainingDebtID
                                    join p in _context.Payments on rd.PaymentID equals p.PaymentID
                                    join ms in _context.Memberships on p.MemberShipID equals ms.MembershipID
                                    join m in _context.Members on ms.MemberID equals m.MemberID
                                    join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                    where dp.IsActive == true
                                    && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                    && dp.CompanyID == companyId
                                    && rd.CompanyID == companyId
                                    && p.CompanyID == companyId
                                    && ms.CompanyID == companyId
                                    && m.CompanyID == companyId
                                    && mt.CompanyID == companyId
                                    select new PaymentHistoryDto
                                    {
                                        PaymentID = dp.DebtPaymentID,
                                        PaymentDate = dp.PaymentDate,
                                        PaymentMethod = dp.PaymentMethod + " (Borç Ödemesi)",
                                        PaymentAmount = dp.PaidAmount,
                                        MembershipType = mt.TypeName,
                                        Branch = mt.Branch,
                                        Name = m.Name,
                                        PhoneNumber = m.PhoneNumber,
                                        IsActive = dp.IsActive
                                    };

            var combinedQuery = paymentsQuery.Union(debtPaymentsQuery);

            if (!string.IsNullOrWhiteSpace(parameters.SearchText))
            {
                var searchText = parameters.SearchText.ToLower();
                combinedQuery = combinedQuery.Where(x =>
                    (x.Name != null && x.Name.ToLower().Contains(searchText)) ||
                    (x.PhoneNumber != null && x.PhoneNumber.Contains(searchText)));
            }

            if (parameters.StartDate.HasValue)
            {
                combinedQuery = combinedQuery.Where(x => x.PaymentDate >= parameters.StartDate.Value.Date);
            }

            if (parameters.EndDate.HasValue)
            {
                var endDateEndOfDay = parameters.EndDate.Value.Date.AddDays(1).AddTicks(-1);
                combinedQuery = combinedQuery.Where(x => x.PaymentDate <= endDateEndOfDay);
            }

            if (!string.IsNullOrWhiteSpace(parameters.PaymentMethod))
            {
                combinedQuery = combinedQuery.Where(x => x.PaymentMethod == parameters.PaymentMethod);
            }

            combinedQuery = combinedQuery.OrderByDescending(x => x.PaymentDate);

            return combinedQuery.ToList();
        }
    }
}
