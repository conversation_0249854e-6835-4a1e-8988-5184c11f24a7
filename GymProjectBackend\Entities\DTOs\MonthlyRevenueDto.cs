using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MonthlyRevenueDto : IDto
    {
        public List<decimal> MonthlyRevenue { get; set; }
        public List<string> Months { get; set; }
        public int Year { get; set; }

        public MonthlyRevenueDto()
        {
            MonthlyRevenue = new List<decimal>();
            Months = new List<string>();
        }
    }
}
