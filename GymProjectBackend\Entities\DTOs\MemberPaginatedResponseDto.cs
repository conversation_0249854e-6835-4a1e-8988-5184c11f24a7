using Core.Utilities.Paging;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MemberPaginatedResponseDto
    {
        public PaginatedResult<Member> PaginatedData { get; set; }
        public int TotalMaleCount { get; set; }
        public int TotalFemaleCount { get; set; }
        // İleride başka toplamlar/istatistikler de eklenebilir
    }
}