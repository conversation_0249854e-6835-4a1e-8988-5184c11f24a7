﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class RemainingDebtDetailDto : IDto
    {
        public int RemainingDebtID { get; set; }
        public int PaymentID { get; set; }
        public string MemberName { get; set; }
        public string PhoneNumber { get; set; }
        public decimal OriginalAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public DateTime LastUpdateDate { get; set; }
        public string PaymentMethod { get; set; }
    }

}
