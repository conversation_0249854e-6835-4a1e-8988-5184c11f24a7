﻿using Castle.Components.DictionaryAdapter.Xml;
using Core.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.Concrete
{
    public class Member : ICompanyEntity
    {
        [Key]
        public int MemberID { get; set; }
        public int CompanyID { get; set; }
        public int? UserID { get; set; }
        public string Name { get; set; }
        public byte Gender { get; set; }
        public string PhoneNumber { get; set; }
        public string? Adress { get; set; }
        public DateOnly? BirthDate { get; set; }
        public string? Email { get; set; }
        public bool? IsActive { get; set; }
        public string? ScanNumber { get; set; }
        public decimal Balance { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? DeletedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }


    }
}
