﻿using Core.Entities;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class LicenseTransaction : IEntity
    {
        [Key]
        public int LicenseTransactionID { get; set; }
        public int UserID { get; set; }
        public int LicensePackageID { get; set; }
        public int? UserLicenseID { get; set; } // İşlemin hangi lisansa ait olduğu
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public DateTime TransactionDate { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? DeletedDate { get; set; }
    }

}
