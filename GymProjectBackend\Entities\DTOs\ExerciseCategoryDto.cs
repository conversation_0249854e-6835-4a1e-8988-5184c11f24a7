using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class ExerciseCategoryDto : IDto
    {
        public int ExerciseCategoryID { get; set; }
        public string CategoryName { get; set; }
        public string? Description { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
    }

    public class ExerciseCategoryAddDto : IDto
    {
        public string CategoryName { get; set; }
        public string? Description { get; set; }
    }

    public class ExerciseCategoryUpdateDto : IDto
    {
        public int ExerciseCategoryID { get; set; }
        public string CategoryName { get; set; }
        public string? Description { get; set; }
        public bool? IsActive { get; set; }
    }
}
