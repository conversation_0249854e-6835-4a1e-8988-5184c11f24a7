using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class WorkoutProgramTemplate : ICompanyEntity
    {
        [Key]
        public int WorkoutProgramTemplateID { get; set; }
        public int CompanyID { get; set; }
        public string ProgramName { get; set; }
        public string? Description { get; set; }
        public string? ExperienceLevel { get; set; } // Başlangıç, Orta, İleri
        public string? TargetGoal { get; set; } // <PERSON>lo <PERSON>, Kilo Verme, Kas Yapma
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? DeletedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
