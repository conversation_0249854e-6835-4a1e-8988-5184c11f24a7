﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class PaymentTotals
    {
        public decimal Cash { get; set; }
        public decimal CreditCard { get; set; }
        public decimal Transfer { get; set; }
        public decimal Debt { get; set; }
        public decimal Total => Cash + CreditCard + Transfer;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }
}
