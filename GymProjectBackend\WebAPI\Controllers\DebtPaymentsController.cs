﻿using Business.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DebtPaymentsController : ControllerBase
    {
        private readonly IDebtPaymentService _debtPaymentService;

        public DebtPaymentsController(IDebtPaymentService debtPaymentService)
        {
            _debtPaymentService = debtPaymentService;
        }

        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _debtPaymentService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}
