﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Core.Utilities.Results
{
    public class DataResult<T> : Result, IDataResult<T>
    {
        public DataResult(T data, bool success, string message) : base(success, message)
        {
            Data = data;
        }
        public DataResult(T data, bool success) : base(success)
        {
            Data = data;
        }

        [JsonConstructor]
        public DataResult(T data, bool success, string message, bool useJsonConstructor) : base(success, message, useJsonConstructor)
        {
            Data = data;
        }

        public T Data { get; private set; }
    }

}