﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MembeFilterDto:IDto
    {
        public int MemberID { get; set; }
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Name { get; set; }
        public byte Gender { get; set; }
        public string PhoneNumber{ get; set; }
        public string TypeName{ get; set; }
        public string Branch{ get; set; }
        public DateTime StartDate{ get; set; }
        public DateTime EndDate{ get; set; }
        public int RemainingDays{ get; set; }
        public bool? IsActive { get; set; }
        public decimal Balance { get; set; }
    }
}
