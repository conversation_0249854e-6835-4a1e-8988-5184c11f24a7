using Core.Entities;
using System;

namespace Entities.DTOs
{
    /// <summary>
    /// Üye profil bilgileri DTO'su
    /// Üyenin görebileceği tüm profil bilgileri
    /// </summary>
    public class MemberProfileDto : IDto
    {
        /// <summary>
        /// User tablosundaki FirstName alanı (güncellenebilir)
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// User tablosundaki LastName alanı (güncellenebilir)
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// User tablosundaki Email alanı (readonly)
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Member tablosundaki Adress alanı (güncellenebilir)
        /// </summary>
        public string Adress { get; set; }

        /// <summary>
        /// Member tablosundaki BirthDate alanı (güncellenebilir)
        /// </summary>
        public DateOnly? BirthDate { get; set; }

        /// <summary>
        /// Member tablosundaki PhoneNumber alanı (readonly)
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// User tablosundaki ProfileImagePath alanı (profil fotoğrafı kontrolü için)
        /// </summary>
        public string ProfileImagePath { get; set; }
    }
}
