﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IRemainingDebtDal : IEntityRepository<RemainingDebt>
    {
        List<RemainingDebtDetailDto> GetRemainingDebtDetails();
        IResult AddDebtPaymentWithBusinessLogic(DebtPaymentDto debtPaymentDto);
        IResult SoftDeleteRemainingDebt(int remainingDebtId, int companyId);
    }

}
