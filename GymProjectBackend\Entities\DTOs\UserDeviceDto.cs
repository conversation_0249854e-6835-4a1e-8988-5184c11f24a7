﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class UserDeviceDto : IDto
    {
        public int Id { get; set; }
        public string DeviceInfo { get; set; }
        public string LastIpAddress { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastUsedAt { get; set; }
        public bool IsCurrentDevice { get; set; }
    }

}
