﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IEntryExitHistoryDal: IEntityRepository<EntryExitHistory>
    {
        IResult AddWithCompanyId(EntryExitHistory entryExitHistory, int companyId);
        IResult UpdateWithCompanyId(EntryExitHistory entryExitHistory, int companyId);
    }
}
