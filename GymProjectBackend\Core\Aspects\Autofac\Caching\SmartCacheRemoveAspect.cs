using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Akıllı cache invalidation aspect
    /// Entity veya Role bazlı otomatik cache temizleme
    /// Configuration'dan kuralları okur
    /// </summary>
    public class SmartCacheRemoveAspect : MethodInterception
    {
        private readonly string _entityName;
        private readonly string _role;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly IHttpContextAccessor _httpContextAccessor;

        /// <summary>
        /// Entity bazlı cache invalidation
        /// </summary>
        /// <param name="entityName">Entity adı (Member, Payment, Product, vb.)</param>
        public SmartCacheRemoveAspect(string entityName)
        {
            _entityName = entityName;
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _httpContextAccessor = ServiceTool.ServiceProvider.GetService<IHttpContextAccessor>();
        }

        /// <summary>
        /// Role bazlı cache invalidation
        /// </summary>
        /// <param name="role">Role adı (member, admin, owner)</param>
        /// <param name="isRoleBased">Role bazlı olduğunu belirtir</param>
        public SmartCacheRemoveAspect(string role, bool isRoleBased)
        {
            if (isRoleBased)
            {
                _role = role;
            }
            else
            {
                _entityName = role;
            }
            
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _httpContextAccessor = ServiceTool.ServiceProvider.GetService<IHttpContextAccessor>();
        }

        public override void Intercept(IInvocation invocation)
        {
            // Metodu çalıştır
            invocation.Proceed();

            // Eğer metod başarılı olduysa cache temizleme yap
            if (IsMethodSuccessful(invocation))
            {
                var companyId = GetCompanyId();
                if (companyId > 0)
                {
                    InvalidateCache(companyId);
                }
            }
        }

        private bool IsMethodSuccessful(IInvocation invocation)
        {
            var result = invocation.ReturnValue;
            
            // IResult interface'ini kontrol et
            if (result != null && result.GetType().GetProperty("Success") != null)
            {
                var successProperty = result.GetType().GetProperty("Success");
                return (bool)successProperty.GetValue(result);
            }
            
            return true; // Default olarak başarılı kabul et
        }

        private int GetCompanyId()
        {
            try
            {
                // Önce CompanyContext'ten dene
                var companyId = _companyContext.GetCompanyId();
                if (companyId > 0)
                {
                    return companyId;
                }

                // CompanyContext başarısızsa JWT'den direkt al
                var companyIdClaim = _httpContextAccessor.HttpContext?.User?.Claims?
                    .FirstOrDefault(c => c.Type == "CompanyId");

                if (companyIdClaim != null && int.TryParse(companyIdClaim.Value, out int jwtCompanyId))
                {
                    return jwtCompanyId;
                }

                return -1;
            }
            catch
            {
                return -1;
            }
        }

        private void InvalidateCache(int companyId)
        {
            try
            {
                string[] patterns;

                if (!string.IsNullOrEmpty(_entityName))
                {
                    // Entity bazlı cache invalidation
                    patterns = CacheInvalidationConfig.GetCachePatternsForEntity(_entityName, companyId);
                }
                else if (!string.IsNullOrEmpty(_role))
                {
                    // Role bazlı cache invalidation
                    patterns = CacheInvalidationConfig.GetCachePatternsForRole(_role, companyId);
                }
                else
                {
                    return;
                }

                foreach (var pattern in patterns)
                {
                    var removedCount = _cacheService.RemoveByPattern(pattern);
                    System.Diagnostics.Debug.WriteLine($"Cache pattern '{pattern}' removed {removedCount} keys");
                }

                var invalidationType = !string.IsNullOrEmpty(_entityName) ? $"Entity: {_entityName}" : $"Role: {_role}";
                System.Diagnostics.Debug.WriteLine($"Smart cache invalidation completed for {invalidationType}, CompanyId: {companyId}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Smart cache invalidation error: {ex.Message}");
                // Cache hatası uygulamayı durdurmasın
            }
        }
    }
}
