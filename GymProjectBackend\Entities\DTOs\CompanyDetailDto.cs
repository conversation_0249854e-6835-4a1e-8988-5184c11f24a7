﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class CompanyDetailDto:IDto
    {
        public int CompanyUserId { get; set; }
        public int UserCompanyId{ get; set; }
        public int CompanyId{ get; set; }
        public int CompanyAdressId{ get; set; }
        public bool? IsActive { get; set; }
        public string CompanyUserName { get; set; }
        public string CityName { get; set; }
        public string TownName { get; set; }
        public string CompanyUserPhoneNumber { get; set; }
        public string CompanyPhoneNumber { get; set; }
        public string CompanyUserEmail { get; set; }
        public string CompanyAdress{ get; set; }
        public string CompanyName{ get; set; }
    }
}
