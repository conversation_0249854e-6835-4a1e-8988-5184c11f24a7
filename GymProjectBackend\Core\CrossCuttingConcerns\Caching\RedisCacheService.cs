using Core.CrossCuttingConcerns.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Redis cache service implementation with error handling and fallback
    /// Multi-tenant aware with JSON serialization support
    /// </summary>
    public class RedisCacheService : ICacheService
    {
        private readonly IDatabase _database;
        private readonly IConnectionMultiplexer _redis;
        private readonly ILogService _logger;
        private readonly TimeSpan _defaultExpiry = TimeSpan.FromMinutes(30);

        public RedisCacheService(IDatabase database, IConnectionMultiplexer redis, ILogService logger)
        {
            _database = database ?? throw new ArgumentNullException(nameof(database));
            _redis = redis ?? throw new ArgumentNullException(nameof(redis));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public T Get<T>(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return default(T);

                var value = _database.StringGet(key);
                
                if (!value.HasValue)
                    return default(T);

                // String type için direkt dönüş
                if (typeof(T) == typeof(string))
                    return (T)(object)value.ToString();

                // Diğer tipler için JSON deserialization - TypeNameHandling.Auto ile
                var settings = new JsonSerializerSettings
                {
                    TypeNameHandling = TypeNameHandling.Auto,
                    NullValueHandling = NullValueHandling.Include
                };
                return JsonConvert.DeserializeObject<T>(value, settings);
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis Get hatası - Key: {key}, Error: {ex.Message}");
                return default(T);
            }
        }

        public async Task<T> GetAsync<T>(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return default(T);

                var value = await _database.StringGetAsync(key);
                
                if (!value.HasValue)
                    return default(T);

                // String type için direkt dönüş
                if (typeof(T) == typeof(string))
                    return (T)(object)value.ToString();

                // Diğer tipler için JSON deserialization - TypeNameHandling.Auto ile
                var settings = new JsonSerializerSettings
                {
                    TypeNameHandling = TypeNameHandling.Auto,
                    NullValueHandling = NullValueHandling.Include
                };
                return JsonConvert.DeserializeObject<T>(value, settings);
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis GetAsync hatası - Key: {key}, Error: {ex.Message}");
                return default(T);
            }
        }

        public void Set<T>(string key, T value, TimeSpan? expiry = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key) || value == null)
                    return;

                var expiryTime = expiry ?? _defaultExpiry;
                
                // String type için direkt kayıt
                if (typeof(T) == typeof(string))
                {
                    _database.StringSet(key, value.ToString(), expiryTime);
                }
                else
                {
                    // Diğer tipler için JSON serialization
                    var jsonValue = JsonConvert.SerializeObject(value, Formatting.None);
                    _database.StringSet(key, jsonValue, expiryTime);
                }

                _logger.Info($"Redis Set başarılı - Key: {key}, Expiry: {expiryTime.TotalMinutes} dakika");
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis Set hatası - Key: {key}, Error: {ex.Message}");
                // Fallback: Exception fırlatma, sadece log
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key) || value == null)
                    return;

                var expiryTime = expiry ?? _defaultExpiry;
                
                // String type için direkt kayıt
                if (typeof(T) == typeof(string))
                {
                    await _database.StringSetAsync(key, value.ToString(), expiryTime);
                }
                else
                {
                    // Diğer tipler için JSON serialization
                    var jsonValue = JsonConvert.SerializeObject(value, Formatting.None);
                    await _database.StringSetAsync(key, jsonValue, expiryTime);
                }

                _logger.Info($"Redis SetAsync başarılı - Key: {key}, Expiry: {expiryTime.TotalMinutes} dakika");
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis SetAsync hatası - Key: {key}, Error: {ex.Message}");
                // Fallback: Exception fırlatma, sadece log
            }
        }

        public bool Remove(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                var result = _database.KeyDelete(key);
                _logger.Info($"Redis Remove - Key: {key}, Silindi: {result}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis Remove hatası - Key: {key}, Error: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> RemoveAsync(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                var result = await _database.KeyDeleteAsync(key);
                _logger.Info($"Redis RemoveAsync - Key: {key}, Silindi: {result}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis RemoveAsync hatası - Key: {key}, Error: {ex.Message}");
                return false;
            }
        }

        public bool Exists(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                return _database.KeyExists(key);
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis Exists hatası - Key: {key}, Error: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                return await _database.KeyExistsAsync(key);
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis ExistsAsync hatası - Key: {key}, Error: {ex.Message}");
                return false;
            }
        }

        public IEnumerable<string> GetKeys(string pattern = "*")
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                return server.Keys(pattern: pattern).Select(k => k.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis GetKeys hatası - Pattern: {pattern}, Error: {ex.Message}");
                return new List<string>();
            }
        }

        public async Task<IEnumerable<string>> GetKeysAsync(string pattern = "*")
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                var keys = server.KeysAsync(pattern: pattern);
                var result = new List<string>();
                
                await foreach (var key in keys)
                {
                    result.Add(key.ToString());
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis GetKeysAsync hatası - Pattern: {pattern}, Error: {ex.Message}");
                return new List<string>();
            }
        }

        public Dictionary<string, T> GetByPattern<T>(string pattern)
        {
            try
            {
                var result = new Dictionary<string, T>();
                var keys = GetKeys(pattern);

                foreach (var key in keys)
                {
                    var value = Get<T>(key);
                    if (value != null)
                    {
                        result[key] = value;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis GetByPattern hatası - Pattern: {pattern}, Error: {ex.Message}");
                return new Dictionary<string, T>();
            }
        }

        public long RemoveByPattern(string pattern)
        {
            try
            {
                var keys = GetKeys(pattern).ToArray();
                if (keys.Length == 0)
                    return 0;

                var redisKeys = keys.Select(k => (RedisKey)k).ToArray();
                var deletedCount = _database.KeyDelete(redisKeys);
                
                _logger.Info($"Redis RemoveByPattern - Pattern: {pattern}, Silinen: {deletedCount}");
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis RemoveByPattern hatası - Pattern: {pattern}, Error: {ex.Message}");
                return 0;
            }
        }

        public async Task<long> RemoveByPatternAsync(string pattern)
        {
            try
            {
                var keys = (await GetKeysAsync(pattern)).ToArray();
                if (keys.Length == 0)
                    return 0;

                var redisKeys = keys.Select(k => (RedisKey)k).ToArray();
                var deletedCount = await _database.KeyDeleteAsync(redisKeys);
                
                _logger.Info($"Redis RemoveByPatternAsync - Pattern: {pattern}, Silinen: {deletedCount}");
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis RemoveByPatternAsync hatası - Pattern: {pattern}, Error: {ex.Message}");
                return 0;
            }
        }

        public CacheHealthInfo GetHealthInfo()
        {
            var healthInfo = new CacheHealthInfo();
            
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                var startTime = DateTime.UtcNow;
                var pong = server.Ping();
                var endTime = DateTime.UtcNow;

                healthInfo.IsConnected = true;
                healthInfo.ServerInfo = server.EndPoint.ToString();
                healthInfo.PingTime = pong;
                healthInfo.DatabaseSize = server.DatabaseSize();
            }
            catch (Exception ex)
            {
                healthInfo.IsConnected = false;
                healthInfo.ErrorMessage = ex.Message;
                _logger.Error($"Redis Health Check hatası: {ex.Message}");
            }

            return healthInfo;
        }

        public bool IsHealthy()
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                var pong = server.Ping();
                return pong.TotalMilliseconds < 1000; // 1 saniyeden az ping süresi
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis IsHealthy hatası: {ex.Message}");
                return false;
            }
        }
    }
}
