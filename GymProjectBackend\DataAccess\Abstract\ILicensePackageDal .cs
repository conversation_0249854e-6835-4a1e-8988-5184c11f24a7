﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ILicensePackageDal : IEntityRepository<LicensePackage>
    {
        IResult AddLicensePackage(LicensePackage licensePackage);
        IResult UpdateLicensePackage(LicensePackage licensePackage);

        // SOLID prensiplerine uygun: Complex business operations
        IResult SoftDeleteLicensePackage(int licensePackageId);
    }

}
