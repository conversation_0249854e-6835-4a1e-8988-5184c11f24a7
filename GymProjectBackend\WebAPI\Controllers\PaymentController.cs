﻿using Business.Abstract;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting; // Rate Limiting için eklendi

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PaymentController : ControllerBase
    {
        IPaymentService _paymentService;

        public PaymentController(IPaymentService paymentService)
        {
            _paymentService = paymentService;
        }
        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _paymentService.GetAll();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpPost("add")]
        public IActionResult Add(Payment payment)
        {
            var result = _paymentService.Add(payment);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }

        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _paymentService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }
        [HttpPost("update")]
        public IActionResult Update(Payment payment)
        {
            var result = _paymentService.Update(payment);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }
        [HttpGet("getpaymenthistory")]
        public IActionResult GetPaymentHistory()
        {
            var result = _paymentService.GetPaymentHistory();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("GetDebtorMembers")]
        public IActionResult GetDebtorMembers()
        {
            var result = _paymentService.GetDebtorMembers();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpPost("updatestatus/{id}")]
        public IActionResult UpdatePaymentStatus(int id, [FromBody] PaymentUpdateModelDto updateDto)
        {
            var result = _paymentService.UpdatePaymentStatus(id, updateDto.PaymentMethod);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getpaymenthistorypaginated")]
        public IActionResult GetPaymentHistoryPaginated([FromQuery] PaymentPagingParameters parameters = null)
        {
            if (parameters == null)
            {
                parameters = new PaymentPagingParameters();
            }

            var result = _paymentService.GetPaymentHistoryPaginated(parameters);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getpaymenttotals")]
        public IActionResult GetPaymentTotals([FromQuery] PaymentPagingParameters parameters = null)
        {
            if (parameters == null)
            {
                parameters = new PaymentPagingParameters();
            }
            var result = _paymentService.GetPaymentTotals(parameters);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getmonthlyrevenue")]
        public IActionResult GetMonthlyRevenue([FromQuery] int year = 0)
        {
            if (year == 0)
            {
                year = DateTime.Now.Year;
            }
            var result = _paymentService.GetMonthlyRevenue(year);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getallpaymenthistoryfiltered")] // Yeni endpoint
        public IActionResult GetAllPaymentHistoryFiltered([FromQuery] PaymentPagingParameters parameters = null)
        {
            if (parameters == null)
            {
                parameters = new PaymentPagingParameters(); // Boş parametre nesnesi oluştur
            }
            var result = _paymentService.GetAllPaymentHistoryFiltered(parameters);
            return result.Success ? Ok(result) : BadRequest(result);
        }
    }
}
