﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MembershipFreezeDto : IDto
    {
        public int MembershipID { get; set; }
        public string MemberName { get; set; }
        public string PhoneNumber { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime FreezeStartDate { get; set; }
        public DateTime FreezeEndDate { get; set; }
        public int FreezeDays { get; set; }
        public string Branch { get; set; }
    }

    public class MembershipFreezeRequestDto : IDto
    {
        public int MembershipID { get; set; }
        public int FreezeDays { get; set; }
    }
}
