﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.Concrete
{
    public class Payment : ICompanyEntity
    {
        [Key]
        public int PaymentID { get; set; }
        public int MemberShipID { get; set; }
        public int CompanyID { get; set; }
        public DateTime PaymentDate { get; set; }
        public decimal PaymentAmount { get; set; }
        public string? PaymentMethod { get; set; }  // nullable
        public string? OriginalPaymentMethod { get; set; }  // nullable
        public string? FinalPaymentMethod { get; set; }  // nullable
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? DeletedDate { get; set; }
        public string? PaymentStatus { get; set; }  // nullable
    }
}
