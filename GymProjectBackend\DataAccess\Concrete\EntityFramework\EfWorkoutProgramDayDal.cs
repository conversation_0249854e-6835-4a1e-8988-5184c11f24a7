using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfWorkoutProgramDayDal : EfCompanyEntityRepositoryBase<WorkoutProgramDay, GymContext>, IWorkoutProgramDayDal
    {
        private readonly ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfWorkoutProgramDayDal(ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }
    }
}
