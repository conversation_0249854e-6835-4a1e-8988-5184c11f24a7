﻿using Business.Abstract;
using Entities.Concrete;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EntryExitHistoryController : ControllerBase
    {
        IEntryExitHistoryService _entryExitHistoryService;

        public EntryExitHistoryController(IEntryExitHistoryService entryExitHistoryService)
        {
            _entryExitHistoryService = entryExitHistoryService;
        }

        //[HttpGet("getall")]
        //public IActionResult GetAll()
        //{
        //    var result = _entryExitHistoryService.GetAll();
        //    if (result.Success)
        //    {
        //        return Ok(result);
        //    }
        //    return BadRequest(result);
        //}
        //[HttpPost("add")]
        //public IActionResult Add(EntryExitHistory entryExitHistory)
        //{
        //    var result = _entryExitHistoryService.Add(entryExitHistory);
        //    if (result.Success)
        //    {
        //        return Ok(result);
        //    }
        //    return BadRequest();
        //}
        //[HttpDelete("delete")]
        //public IActionResult Delete(int id)
        //{
        //    var result = _entryExitHistoryService.Delete(id);
        //    if (result.Success)
        //    {
        //        return Ok(result);
        //    }
        //    return BadRequest();
        //}
        //[HttpPost("update")]
        //public IActionResult Update(EntryExitHistory entryExitHistory)
        //{
        //    var result = _entryExitHistoryService.Update(entryExitHistory);
        //    if (result.Success)
        //    {
        //        return Ok(result);
        //    }
        //    return BadRequest();
        //}
    }
}
