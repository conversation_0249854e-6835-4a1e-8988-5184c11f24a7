using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IExerciseCategoryDal : IEntityRepository<ExerciseCategory>
    {
        List<ExerciseCategoryDto> GetAllCategories();
        List<ExerciseCategoryDto> GetActiveCategories();
        ExerciseCategoryDto GetCategoryById(int categoryId);
        IResult AddExerciseCategory(ExerciseCategoryAddDto categoryAddDto);
        IResult UpdateExerciseCategory(ExerciseCategoryUpdateDto categoryUpdateDto);
        IResult DeleteExerciseCategory(int categoryId);
    }
}
