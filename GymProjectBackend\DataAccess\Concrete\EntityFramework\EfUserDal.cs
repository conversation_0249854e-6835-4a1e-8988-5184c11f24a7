using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserDal : EfEntityRepositoryBase<User, GymContext>, IUserDal
    {
        // Constructor injection (Scalability i�in)
        public EfUserDal(GymContext context) : base(context)
        {
        }

        public List<OperationClaim> GetClaims(User user)
        {
            var result = from OperationClaim in _context.OperationClaims
                         join UserOperationClaim in _context.UserOperationClaims
                         on OperationClaim.OperationClaimId equals UserOperationClaim.OperationClaimId
                         where UserOperationClaim.UserId == user.UserID
                         select new OperationClaim { OperationClaimId= OperationClaim.OperationClaimId, Name = OperationClaim.Name };
            return result.ToList();
        }

        /// <summary>
        /// Member rol� olmayan t�m kullan�c�lar� getirir (K���k sistemler i�in)
        /// </summary>
        public List<User> GetNonMembers()
        {
            // DI kullan�l�yor - Scalability optimized
            // Member rol�n�n ID'sini al (Cache'lenebilir)
            var memberRoleId = _context.OperationClaims
                    .Where(oc => oc.Name == "member" && oc.IsActive == true)
                    .Select(oc => oc.OperationClaimId)
                    .FirstOrDefault();

                if (memberRoleId == 0)
                {
                    // Member rol� yoksa t�m aktif kullan�c�lar� d�ner
                    return _context.Users
                        .Where(u => u.IsActive)
                        .OrderBy(u => u.FirstName)
                        .ThenBy(u => u.LastName)
                        .ToList();
                }

                // Member rol�ne sahip kullan�c�lar�n ID'lerini al
                var usersWithMemberRole = _context.UserOperationClaims
                    .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                    .Select(uoc => uoc.UserId)
                    .ToHashSet(); // HashSet performans i�in

                // Member rol� olmayan kullan�c�lar� getir
                return _context.Users
                    .Where(u => u.IsActive == true && !usersWithMemberRole.Contains(u.UserID))
                    .OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName)
                    .ToList();
        }

        /// <summary>
        /// Member rol� olmayan kullan�c�lar� sayfal� olarak getirir (10K+ kullan�c� i�in optimize)
        /// </summary>
        public List<User> GetNonMembersPaginated(int page, int pageSize, string searchTerm)
        {
            // DI kullan�l�yor - Scalability optimized
            // Member rol�n�n ID'sini al
            var memberRoleId = _context.OperationClaims
                    .Where(oc => oc.Name == "member" && oc.IsActive == true)
                    .Select(oc => oc.OperationClaimId)
                    .FirstOrDefault();

                var query = _context.Users.AsQueryable();

                // Aktif kullan�c�lar
                query = query.Where(u => u.IsActive);

                // Member rol� varsa, member olmayan kullan�c�lar� filtrele
                if (memberRoleId > 0)
                {
                    var usersWithMemberRole = _context.UserOperationClaims
                        .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                        .Select(uoc => uoc.UserId);

                    query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
                }

                // Arama terimi varsa filtrele
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    var lowerSearchTerm = searchTerm.ToLower();
                    query = query.Where(u =>
                        u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                        u.LastName.ToLower().Contains(lowerSearchTerm) ||
                        u.Email.ToLower().Contains(lowerSearchTerm));
                }

                // Sayfalama ve s�ralama
                return query
                    .OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();
        }

        /// <summary>
        /// Member rol� olmayan kullan�c� say�s�n� getirir
        /// </summary>
        public int GetNonMembersCount(string searchTerm)
        {
            // DI kullan�l�yor - Scalability optimized
            // Member rol�n�n ID'sini al
            var memberRoleId = _context.OperationClaims
                    .Where(oc => oc.Name == "member" && oc.IsActive == true)
                    .Select(oc => oc.OperationClaimId)
                    .FirstOrDefault();

                var query = _context.Users.AsQueryable();

                // Aktif kullan�c�lar
                query = query.Where(u => u.IsActive);

                // Member rol� varsa, member olmayan kullan�c�lar� filtrele
                if (memberRoleId > 0)
                {
                    var usersWithMemberRole = _context.UserOperationClaims
                        .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                        .Select(uoc => uoc.UserId);

                    query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
                }

                // Arama terimi varsa filtrele
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    var lowerSearchTerm = searchTerm.ToLower();
                    query = query.Where(u =>
                        u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                        u.LastName.ToLower().Contains(lowerSearchTerm) ||
                        u.Email.ToLower().Contains(lowerSearchTerm));
                }

                return query.Count();
        }

        /// <summary>
        /// User tablosu g�ncellendi�inde CompanyUser tablosundaki ilgili kay�tlar� senkronize eder
        /// SOLID prensiplerine uygun: Complex database operations DAL katman�nda
        /// </summary>
        public IResult SyncCompanyUserData(User updatedUser, User oldUser)
        {
            try
            {
                // DI kullan�l�yor - Scalability optimized
                // Bu User'a ait CompanyUser kay�tlar�n� bul
                // Email ile e�le�en CompanyUser'lar� bul (��nk� User.Email = CompanyUser.Email)
                var companyUsers = _context.CompanyUsers
                    .Where(cu => cu.Email == oldUser.Email && cu.IsActive == true)
                    .ToList();

                // CompanyUser kay�tlar�n� g�ncelle - SQL ile direkt g�ncelleme
                string newEmail = updatedUser.Email;
                string newFullName = $"{updatedUser.FirstName} {updatedUser.LastName}".Trim();

                foreach (var companyUser in companyUsers)
                {
                    bool emailChanged = companyUser.Email != newEmail;
                    bool nameChanged = companyUser.Name != newFullName;

                    if (emailChanged || nameChanged)
                    {
                        // SQL ile direkt g�ncelleme yap - Entity Framework context'ini hi� kullanma
                        string updateSql = @"
                            UPDATE CompanyUsers
                            SET Email = @Email,
                                Name = @Name,
                                UpdatedDate = @UpdatedDate
                            WHERE CompanyUserID = @CompanyUserID";

                        _context.Database.ExecuteSqlRaw(updateSql,
                            new Microsoft.Data.SqlClient.SqlParameter("@Email", newEmail),
                            new Microsoft.Data.SqlClient.SqlParameter("@Name", newFullName),
                            new Microsoft.Data.SqlClient.SqlParameter("@UpdatedDate", DateTime.Now),
                            new Microsoft.Data.SqlClient.SqlParameter("@CompanyUserID", companyUser.CompanyUserID));
                    }
                }

                return new SuccessResult("CompanyUser verileri ba�ar�yla senkronize edildi.");
            }
            catch (Exception ex)
            {
                // Hata durumunda detayl� log ve error result d�ner
                return new ErrorResult($"CompanyUser senkronizasyonu s�ras�nda hata olu�tu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Profile image path g�ncelleme i�lemi DAL katman�nda
        /// </summary>
        public IResult UpdateProfileImagePath(int userId, string imagePath)
        {
            try
            {
                // DI kullan�l�yor - Scalability optimized
                var user = _context.Users.FirstOrDefault(u => u.UserID == userId && u.IsActive);
                if (user == null)
                {
                    return new ErrorResult("Kullan�c� bulunamad�.");
                }

                user.ProfileImagePath = imagePath;
                user.UpdatedDate = DateTime.Now;
                _context.Users.Update(user);
                _context.SaveChanges();

                return new SuccessResult("Profil foto�raf� yolu ba�ar�yla g�ncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Profil foto�raf� yolu g�ncellenirken hata olu�tu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Profile image path temizleme i�lemi DAL katman�nda
        /// </summary>
        public IResult ClearProfileImagePath(int userId)
        {
            try
            {
                // DI kullan�l�yor - Scalability optimized
                var user = _context.Users.FirstOrDefault(u => u.UserID == userId && u.IsActive);
                if (user == null)
                {
                    return new ErrorResult("Kullan�c� bulunamad�.");
                }

                user.ProfileImagePath = null;
                user.UpdatedDate = DateTime.Now;
                _context.Users.Update(user);
                _context.SaveChanges();

                return new SuccessResult("Profil foto�raf� yolu ba�ar�yla temizlendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Profil foto�raf� yolu temizlenirken hata olu�tu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Validation logic DAL katman�nda
        /// </summary>
        public IDataResult<User> GetUserByIdWithValidation(int userId)
        {
            try
            {
                // DI kullan�l�yor - Scalability optimized
                var user = _context.Users.FirstOrDefault(u => u.UserID == userId && u.IsActive);
                if (user == null)
                {
                    return new ErrorDataResult<User>("Kullan�c� bulunamad�");
                }
                return new SuccessDataResult<User>(user);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<User>($"Kullan�c� getirilirken hata olu�tu: {ex.Message}");
            }
        }

    }
}
