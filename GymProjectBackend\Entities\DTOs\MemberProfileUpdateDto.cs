using Core.Entities;
using System;

namespace Entities.DTOs
{
    /// <summary>
    /// Üye profil güncelleme DTO'su
    /// Üyenin kendi güncelleyebileceği bilgiler
    /// </summary>
    public class MemberProfileUpdateDto : IDto
    {
        /// <summary>
        /// User tablosundaki FirstName alanı
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// User tablosundaki LastName alanı
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// Member tablosundaki Adress alanı (opsiyonel)
        /// </summary>
        public string? Adress { get; set; }

        /// <summary>
        /// Member tablosundaki BirthDate alanı
        /// </summary>
        public DateOnly? BirthDate { get; set; }
    }
}
