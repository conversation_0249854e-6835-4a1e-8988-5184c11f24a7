using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Core.Entities.Concrete;
using Core.Utilities.Security.Hashing;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using MemberFilter = Entities.DTOs.MemberFilter;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMemberDal : EfCompanyEntityRepositoryBase<Member, GymContext>, IMemberDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfMemberDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        public MemberDetailWithHistoryDto GetMemberDetailById(int memberId)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Get member basic information
                var member = _context.Members
                    .FirstOrDefault(m => m.MemberID == memberId && m.CompanyID == companyId);

                if (member == null)
                {
                    return null;
                }

                // Get memberships with membership type details
                var memberships = (from ms in _context.Memberships
                                  join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                  where ms.MemberID == memberId
                                        && ms.CompanyID == companyId
                                        && mt.CompanyID == companyId
                                        && ms.DeletedDate == null
                                        && mt.DeletedDate == null
                                  select new MembershipHistoryDto
                                  {
                                      MembershipID = ms.MembershipID,
                                      MembershipTypeID = ms.MembershipTypeID,
                                      TypeName = mt.TypeName,
                                      Branch = mt.Branch,
                                      Day = mt.Day,
                                      Price = mt.Price,
                                      StartDate = ms.StartDate,
                                      EndDate = ms.EndDate,
                                      IsActive = ms.IsActive == true,
                                      IsFrozen = ms.IsFrozen,
                                      FreezeStartDate = ms.FreezeStartDate,
                                      FreezeEndDate = ms.FreezeEndDate,
                                      FreezeDays = ms.FreezeDays,
                                      OriginalEndDate = ms.OriginalEndDate,
                                      CreationDate = ms.CreationDate
                                  }).ToList();

                // Get payments with membership type details
                var payments = (from p in _context.Payments
                               join ms in _context.Memberships on p.MemberShipID equals ms.MembershipID
                               join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                               where ms.MemberID == memberId
                                     && p.CompanyID == companyId
                                     && ms.CompanyID == companyId
                                     && mt.CompanyID == companyId
                                     && ms.DeletedDate == null
                                     && p.DeletedDate == null
                                     && mt.DeletedDate == null
                               select new PaymentHistoryItemDto
                               {
                                   PaymentID = p.PaymentID,
                                   MembershipID = ms.MembershipID,
                                   MembershipTypeName = mt.TypeName,
                                   Branch = mt.Branch,
                                   PaymentDate = p.PaymentDate,
                                   PaymentAmount = p.PaymentAmount,
                                   PaymentMethod = p.PaymentMethod,
                                   PaymentStatus = p.PaymentStatus
                               }).ToList();

                // Get entry/exit history with membership type details
                var entryExitHistory = (from eh in _context.EntryExitHistories
                                       join ms in _context.Memberships on eh.MembershipID equals ms.MembershipID
                                       join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                       where ms.MemberID == memberId
                                             && eh.CompanyID == companyId
                                             && ms.CompanyID == companyId
                                             && mt.CompanyID == companyId
                                             && ms.DeletedDate == null
                                             && mt.DeletedDate == null
                                       select new EntryExitHistoryItemDto
                                       {
                                           EntryExitID = eh.EntryExitID,
                                           MembershipID = ms.MembershipID,
                                           MembershipTypeName = mt.TypeName,
                                           Branch = mt.Branch,
                                           EntryDate = eh.EntryDate,
                                           ExitDate = eh.ExitDate
                                       }).ToList();

                // Get membership freeze history with membership type details
                var freezeHistory = (from fh in _context.MembershipFreezeHistory
                                    join ms in _context.Memberships on fh.MembershipID equals ms.MembershipID
                                    join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                    where ms.MemberID == memberId
                                          && fh.CompanyID == companyId
                                          && ms.CompanyID == companyId
                                          && mt.CompanyID == companyId
                                          && ms.DeletedDate == null
                                          && mt.DeletedDate == null
                                    select new MembershipFreezeHistoryItemDto
                                    {
                                        FreezeHistoryID = fh.FreezeHistoryID,
                                        MembershipID = ms.MembershipID,
                                        MembershipTypeName = mt.TypeName,
                                        Branch = mt.Branch,
                                        StartDate = fh.StartDate,
                                        PlannedEndDate = fh.PlannedEndDate,
                                        ActualEndDate = fh.ActualEndDate,
                                        FreezeDays = fh.FreezeDays,
                                        UsedDays = fh.UsedDays,
                                        CancellationType = fh.CancellationType
                                    }).ToList();

                // Get the last membership end date
                var lastMembershipEndDate = _context.Memberships
                    .Where(ms => ms.MemberID == memberId && ms.CompanyID == companyId)
                    .OrderByDescending(ms => ms.EndDate)
                    .Select(ms => (DateTime?)ms.EndDate) // Nullable DateTime'a cast et
                    .FirstOrDefault();

                // Create and return the member detail DTO
                return new MemberDetailWithHistoryDto
                {
                    MemberID = member.MemberID,
                    UserID = member.UserID, // Profil fotoğrafı için eklendi
                    Name = member.Name,
                    Gender = member.Gender,
                    PhoneNumber = member.PhoneNumber,
                    Adress = member.Adress,
                    BirthDate = member.BirthDate,
                    Email = member.Email,
                    IsActive = member.IsActive,
                    ScanNumber = member.ScanNumber,
                    Balance = member.Balance,
                    CreationDate = member.CreationDate,
                    Memberships = memberships,
                    Payments = payments,
                    EntryExitHistory = entryExitHistory,
                    FreezeHistory = freezeHistory,
                    LastMembershipEndDate = lastMembershipEndDate // Son üyelik bitiş tarihini ekle
                };
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public PaginatedResult<Member> GetAllPaginated(MemberPagingParameters parameters)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var query = _context.Members.Where(x => x.IsActive == true && x.CompanyID == companyId);  // IsActive ve CompanyID kontrolü eklendi

                // Filtreleme
                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    query = query.Where(x =>
                        x.Name.Contains(parameters.SearchText) ||
                        x.PhoneNumber.Contains(parameters.SearchText));
                }

                if (parameters.Gender.HasValue)
                {
                    // Gelen int? değerini byte'a cast et
                    byte genderByte = (byte)parameters.Gender.Value;
                    query = query.Where(x => x.Gender == genderByte);
                }

                // Sıralama
                query = query.OrderByDescending(x => x.CreationDate);

                return query.ToPaginatedResult(parameters.PageNumber, parameters.PageSize);
            }

            // DI kullanılmıyorsa null döndür (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public List<MemberBirthdayDto> GetUpcomingBirthdays(int days)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var today = DateTime.Now.Date;

                // Doğum günü yaklaşan üyeleri getir
                var result = _context.Members
                    .Where(m => m.IsActive == true && m.CompanyID == companyId && m.BirthDate != null)
                    .AsEnumerable() // Sonraki işlemler için hafızaya al
                    .Select(m => new
                    {
                        Member = m,
                        // Doğum gününe kalan gün sayısını hesapla
                        DaysUntilBirthday = CalculateDaysUntilBirthday(m.BirthDate, today)
                    })
                    .Where(x => x.DaysUntilBirthday <= days) // Belirtilen gün sayısı içinde doğum günü olanları filtrele
                    .OrderBy(x => x.DaysUntilBirthday) // Doğum gününe kalan gün sayısına göre sırala
                    .Select(x => new MemberBirthdayDto
                    {
                        MemberID = x.Member.MemberID,
                        Name = x.Member.Name,
                        PhoneNumber = x.Member.PhoneNumber,
                        BirthDate = x.Member.BirthDate,
                        DaysUntilBirthday = x.DaysUntilBirthday
                    })
                    .ToList();

                return result;
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        // Doğum gününe kalan gün sayısını hesaplayan yardımcı metod
        private int CalculateDaysUntilBirthday(DateOnly? birthDate, DateTime today)
        {
            if (!birthDate.HasValue) return int.MaxValue; // Doğum tarihi yoksa en sona koy

            var birth = birthDate.Value;

            // Bu yılki doğum günü
            var birthThisYear = new DateTime(today.Year, birth.Month, birth.Day);

            // Eğer bu yılki doğum günü geçtiyse, gelecek yılki doğum gününü hesapla
            if (birthThisYear < today)
            {
                birthThisYear = birthThisYear.AddYears(1);
            }

            // Doğum gününe kalan gün sayısı
            var daysUntil = (int)(birthThisYear - today).TotalDays;

            return daysUntil;
        }

        public PaginatedResult<MemberFilter> GetMemberDetailsPaginated(MemberPagingParameters parameters)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var baseQuery = from m in _context.Members
                                join ms in _context.Memberships on m.MemberID equals ms.MemberID
                                join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                where m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                && ms.IsActive == true && ms.EndDate > DateTime.Now && ms.IsFrozen==false
                                && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                                && ms.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                                && mt.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                                select new
                                {
                                    MemberID = m.MemberID,
                                    MembershipID = ms.MembershipID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    Gender = m.Gender,
                                    Branch = mt.Branch,
                                    RemainingDays = ms.StartDate > DateTime.Now
                                        ? EF.Functions.DateDiffDay(ms.StartDate, ms.EndDate) // Gelecekteki başlangıç için
                                        : EF.Functions.DateDiffDay(DateTime.Now, ms.EndDate), // Başlamış üyelik için
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate,
                                    IsActive = ms.IsActive == true,
                                    UpdatedDate = ms.UpdatedDate, // Üyeliğin güncellenme tarihi
                                    CreationDate = ms.CreationDate, // Üyeliğin oluşturulma tarihi
                                    IsFutureStartDate = ms.StartDate > DateTime.Now // Başlangıç tarihi gelecekte mi
                                };

                // Önce verileri hafızaya alıp gruplandırma yapıyoruz
                var groupedQuery = baseQuery.AsEnumerable()
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.Gender, x.Branch })
                    .Select(g => new MemberFilter
                    {
                        MemberID = g.Key.MemberID,
                        MembershipID = g.Max(x => x.MembershipID), // En son eklenen üyeliğin ID'si
                        Name = g.Key.Name,
                        PhoneNumber = g.Key.PhoneNumber,
                        Gender = g.Key.Gender,
                        Branch = g.Key.Branch,
                        RemainingDays = g.Sum(x => x.RemainingDays), // Kalan günleri topluyoruz
                        StartDate = g.Min(x => x.StartDate), // En erken başlangıç tarihi
                        EndDate = g.Max(x => x.EndDate), // En geç bitiş tarihi
                        IsActive = true,
                        UpdatedDate = g.Max(x => x.UpdatedDate ?? x.CreationDate), // En son güncelleme tarihi veya oluşturma tarihi
                        IsFutureStartDate = g.Any(x => x.IsFutureStartDate) // Herhangi bir üyeliğin başlangıç tarihi gelecekte mi
                    });

                // Filtreleme işlemleri
                var filteredQuery = groupedQuery.AsQueryable();

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    var searchText = parameters.SearchText.ToLower();
                    filteredQuery = filteredQuery.Where(x =>
                        x.Name.ToLower().Contains(searchText) ||
                        x.PhoneNumber.Contains(searchText));
                }

                if (parameters.Gender.HasValue)
                {
                    // Gelen int? değerini byte'a cast et
                    byte genderByte = (byte)parameters.Gender.Value;
                    filteredQuery = filteredQuery.Where(x => x.Gender == genderByte);
                }

                if (!string.IsNullOrWhiteSpace(parameters.Branch))
                {
                    filteredQuery = filteredQuery.Where(x => x.Branch == parameters.Branch);
                }

                // Sıralama - Önce UpdatedDate'e göre, sonra MembershipID'ye göre azalan sıralama
                var orderedQuery = filteredQuery
                    .OrderByDescending(x => x.UpdatedDate)
                    .ThenByDescending(x => x.MembershipID);

                // Sayfalama için toplam kayıt sayısını al
                var totalCount = orderedQuery.Count();

                // Sayfalama uygula
                var items = orderedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<MemberFilter>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public List<MembeFilterDto> GetMemberDetails()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var now = DateTime.Now;

                var query = from m in _context.Members
                            join s in _context.Memberships
                            on m.MemberID equals s.MemberID
                            join x in _context.MembershipTypes
                            on s.MembershipTypeID equals x.MembershipTypeID
                            where m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                            && s.IsActive == true
                            && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                            && s.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                            && x.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                            select new
                            {
                                m.MemberID,
                                s.MembershipID,
                                x.MembershipTypeID,
                                m.Name,
                                m.Gender,
                                m.PhoneNumber,
                                x.TypeName,
                                x.Branch,
                                s.StartDate,
                                s.EndDate,
                                s.IsActive,
                                m.Balance,
                            };

                var results = query.AsEnumerable();

                var groupedResults = results
                    .GroupBy(r => new { r.MemberID, r.Name, r.Gender, r.PhoneNumber, r.Branch })
                    .Select(g => new MembeFilterDto
                    {
                        MemberID = g.Key.MemberID,
                        MembershipID = g.Max(r => r.MembershipID),
                        MembershipTypeID = g.Max(r => r.MembershipTypeID),
                        Name = g.Key.Name,
                        Gender = g.Key.Gender,
                        PhoneNumber = g.Key.PhoneNumber,
                        Branch = g.Key.Branch,
                        TypeName = string.Join(", ", g.Select(r => r.TypeName).Distinct()),
                        StartDate = g.Min(r => r.StartDate),
                        EndDate = g.Max(r => r.EndDate),
                        RemainingDays = g.Sum(r =>
                            (r.EndDate > now) ?
                            (int)Math.Ceiling((r.EndDate - now).TotalDays) : 0),
                        IsActive = true
                    })
                    .ToList();

                return groupedResults;
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public List<GetActiveMemberDto> GetActiveMembers()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from m in _context.Members
                             where m.IsActive == true
                             && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                             select new GetActiveMemberDto
                             {
                                 MemberID = m.MemberID,
                                 Name = m.Name,
                                 Gender = m.Gender,
                                 PhoneNumber = m.PhoneNumber,
                                 IsActive = m.IsActive,
                                 Adress = m.Adress,
                                 BirthDate = m.BirthDate,
                                 Email = m.Email,
                                 ScanNumber = m.ScanNumber,
                                 Balance = m.Balance,
                             };
                return result.ToList();
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public List<MemberEntryExitHistoryDto> GetMemberEntryExitHistory()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var currentTime = DateTime.Now;
                var result = from a in _context.Members
                             join x in _context.Memberships
                             on a.MemberID equals x.MemberID
                             join s in _context.EntryExitHistories
                             on x.MembershipID equals s.MembershipID
                             where a.IsActive == true // Member'ın aktif olması kontrolü eklendi
                             && s.IsActive == true
                             && s.EntryDate.HasValue
                             && EF.Functions.DateDiffMinute(s.EntryDate.Value, currentTime) <= 300
                             && a.CompanyID == companyId // Şirket ID'sine göre filtrele
                             && x.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                             && s.CompanyID == companyId // Giriş-çıkış kayıtlarının da aynı şirkete ait olduğundan emin ol
                             select new MemberEntryExitHistoryDto
                             {
                                 MemberID = a.MemberID,
                                 MemberName = a.Name,
                                 EntryDate = s.EntryDate,
                                 IsActive = s.IsActive,
                             };
                return result.ToList();
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public List<MemberRemainingDayDto> GetMemberRemainingDay()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var now = DateTime.Now;
                var result = (from a in _context.Members
                              join b in _context.Memberships on a.MemberID equals b.MemberID
                              join c in _context.MembershipTypes on b.MembershipTypeID equals c.MembershipTypeID
                              where a.IsActive == true // Member'ın aktif olması kontrolü eklendi
                              && b.IsActive == true
                              && a.CompanyID == companyId // Şirket ID'sine göre filtrele
                              && b.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                              && c.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                              select new
                              {
                                  a.MemberID,
                                  a.Name,
                                  a.PhoneNumber,
                                  b.StartDate,
                                  b.EndDate,
                                  c.Branch
                              })
                              .AsEnumerable()
                              .Select(x => new
                              {
                                  MemberID = x.MemberID,
                                  MemberName = x.Name,
                                  PhoneNumber = x.PhoneNumber,
                                  StartDate = x.StartDate,
                                  EndDate = x.EndDate,
                                  RemainingDays = (x.EndDate > now) ? (int)Math.Ceiling((x.EndDate - now).TotalDays) : 0,
                                  Branch = x.Branch
                              })
                              .ToList();

                var groupedResult = result
                    .GroupBy(r => new { r.MemberID, r.Branch })
                    .Select(g => new MemberRemainingDayDto
                    {
                        MemberID = g.Key.MemberID,
                        MemberName = g.First().MemberName,
                        PhoneNumber = g.First().PhoneNumber,
                        StartDate = g.Min(r => r.StartDate),
                        EndDate = g.Max(r => r.EndDate),
                        RemainingDays = g.Sum(r => r.RemainingDays),
                        Message = $"Hoşgeldiniz. {g.First().MemberName.ToUpper()}. {g.Key.Branch} branşında toplam {g.Sum(r => r.RemainingDays)} gün kaldı.",
                        Branch = g.Key.Branch
                    })
                    .Where(dto => dto.RemainingDays > 0 && dto.RemainingDays <= 7)
                    .ToList();

                return groupedResult;
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public Member GetMemberByScanNumber(string scanNumber)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();
                return _context.Members.FirstOrDefault(m => m.ScanNumber == scanNumber && m.CompanyID == companyId && m.IsActive == true);
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public GetMemberQRByPhoneNumberDto GetMemberQRByPhoneNumber(string phoneNumber)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var member = _context.Members.FirstOrDefault(m => m.PhoneNumber == phoneNumber && m.CompanyID == companyId && m.IsActive == true);

                if (member == null)
                {
                    return null; // Veya uygun bir hata/boş DTO döndür
                }

                var now = DateTime.Now;

                var allMembershipsQuery = _context.Memberships
                    .Where(m => m.MemberID == member.MemberID && m.IsActive == true && m.CompanyID == companyId);

                var allMemberships = allMembershipsQuery.OrderBy(m => m.StartDate).ToList();

                var membershipTypesQuery = _context.MembershipTypes.Where(mt => mt.CompanyID == companyId);

                var membershipTypes = membershipTypesQuery.ToDictionary(mt => mt.MembershipTypeID);

                var consolidatedMemberships = new Dictionary<string, MembershipInfo>();

                var frozenMembership = allMemberships.FirstOrDefault(m => m.IsFrozen == true);
                if (frozenMembership != null)
                {
                    return new GetMemberQRByPhoneNumberDto
                    {
                        Name = member.Name,
                        ScanNumber = member.ScanNumber,
                        IsFrozen = true,
                        FreezeEndDate = frozenMembership.FreezeEndDate,
                        RemainingDays = "Dondurulmuş",
                        Memberships = new List<MembershipInfo>()
                    };
                }

                foreach (var membership in allMemberships)
                {
                    if (!membershipTypes.ContainsKey(membership.MembershipTypeID))
                        continue;

                    var membershipType = membershipTypes[membership.MembershipTypeID];
                    var branch = membershipType.Branch;

                    if (!consolidatedMemberships.ContainsKey(branch))
                    {
                        consolidatedMemberships[branch] = new MembershipInfo
                        {
                            Branch = branch,
                            StartDate = membership.StartDate,
                            EndDate = membership.EndDate,
                            RemainingDays = 0
                        };
                    }

                    var existingMembership = consolidatedMemberships[branch];

                    if (membership.StartDate < existingMembership.StartDate)
                    {
                        existingMembership.StartDate = membership.StartDate;
                    }

                    if (membership.EndDate > existingMembership.EndDate)
                    {
                        existingMembership.EndDate = membership.EndDate;
                    }

                    int remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);
                    existingMembership.RemainingDays += Math.Max(0, remainingDays);
                }

                string message;
                if (consolidatedMemberships.Count == 0 || consolidatedMemberships.All(m => m.Value.EndDate <= now))
                {
                    message = "Üyeliğinizin Süresi Dolmuştur";
                }
                else if (consolidatedMemberships.All(m => m.Value.StartDate > now))
                {
                    var earliestMembership = consolidatedMemberships.Values.OrderBy(m => m.StartDate).First();
                    message = $"üyeliğinizin başlamasına {(int)(earliestMembership.StartDate - DateTime.Today).TotalDays} gün vardır.";
                }
                else
                {
                    message = "üyeliğiniz aktif durumdadır.";
                }

                return new GetMemberQRByPhoneNumberDto
                {
                    Name = member.Name,
                    ScanNumber = member.ScanNumber,
                    IsFrozen = false,
                    RemainingDays = consolidatedMemberships.Count > 0 ? consolidatedMemberships.Values.Sum(m => m.RemainingDays).ToString() : "Süresi Dolmuş",
                    Memberships = consolidatedMemberships.Values.ToList(),
                    Message = message
                };
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

       public List<MemberEntryDto> GetTodayEntries(DateTime date)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;
                // Önce temel sorguyu oluşturalım
                var baseQuery = from m in _context.Members
                                join ms in _context.Memberships on m.MemberID equals ms.MemberID
                                join eh in _context.EntryExitHistories on ms.MembershipID equals eh.MembershipID
                                where eh.EntryDate.HasValue && eh.EntryDate.Value.Date == date.Date
                                && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                && ms.IsActive == true
                                && m.CompanyID == companyId // Şirket ID filtresi eklendi
                                && ms.CompanyID == companyId // Şirket ID filtresi eklendi
                                && eh.CompanyID == companyId // Şirket ID filtresi eklendi
                                select new
                                {
                                    MemberID = m.MemberID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    EntryTime = eh.EntryDate.Value,
                                    ExitTime = eh.ExitDate,
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate
                                };

                // Üye bazında gruplayıp, her üyenin tüm üyeliklerini değerlendirelim
                var result = baseQuery.AsEnumerable()
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.EntryTime, x.ExitTime })
                    .Select(g =>
                    {
                        // Üyeliklerin bitiş tarihlerine göre kalan günleri hesaplayalım
                        var totalRemainingDays = _context.Memberships
                            .Where(ms => ms.MemberID == g.Key.MemberID && ms.IsActive == true && ms.CompanyID == companyId) // Şirket ID filtresi eklendi
                            .AsEnumerable()
                            .Where(ms => ms.EndDate > now) // Sadece aktif üyelikler
                            .Sum(ms =>
                            {
                                if (ms.StartDate > now)
                                {
                                    return 0; // Henüz başlamamış üyelikler için 0 gün
                                }
                                return (int)Math.Ceiling((ms.EndDate - now).TotalDays);
                            });

                        return new MemberEntryDto
                        {
                            MemberID = g.Key.MemberID,
                            Name = g.Key.Name,
                            PhoneNumber = g.Key.PhoneNumber,
                            EntryTime = g.Key.EntryTime,
                            ExitTime = g.Key.ExitTime,
                            RemainingDays = totalRemainingDays
                        };
                    })
                    .OrderByDescending(x => x.EntryTime)
                    .ToList();

                return result;
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public PaginatedResult<MemberEntryDto> GetTodayEntriesPaginated(MemberEntryPagingParameters parameters)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;
                var targetDate = parameters.Date ?? DateTime.Today;

                // Önce temel sorguyu oluşturalım
                var baseQuery = from m in _context.Members
                                join ms in _context.Memberships on m.MemberID equals ms.MemberID
                                join eh in _context.EntryExitHistories on ms.MembershipID equals eh.MembershipID
                                where eh.EntryDate.HasValue && eh.EntryDate.Value.Date == targetDate.Date
                                && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                && ms.IsActive == true
                                && m.CompanyID == companyId // Şirket ID filtresi eklendi
                                && ms.CompanyID == companyId // Şirket ID filtresi eklendi
                                && eh.CompanyID == companyId // Şirket ID filtresi eklendi
                                select new
                                {
                                    MemberID = m.MemberID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    EntryTime = eh.EntryDate.Value,
                                    ExitTime = eh.ExitDate,
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate
                                };

                // Arama filtresi varsa uygula
                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    baseQuery = baseQuery.Where(x =>
                        x.Name.Contains(parameters.SearchText) ||
                        x.PhoneNumber.Contains(parameters.SearchText));
                }

                // Üye bazında gruplayıp, her üyenin tüm üyeliklerini değerlendirelim
                var groupedData = baseQuery.AsEnumerable()
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.EntryTime, x.ExitTime })
                    .Select(g =>
                    {
                        // Üyeliklerin bitiş tarihlerine göre kalan günleri hesaplayalım
                        var totalRemainingDays = _context.Memberships
                            .Where(ms => ms.MemberID == g.Key.MemberID && ms.IsActive == true && ms.CompanyID == companyId) // Şirket ID filtresi eklendi
                            .AsEnumerable()
                            .Where(ms => ms.EndDate > now) // Sadece aktif üyelikler
                            .Sum(ms =>
                            {
                                if (ms.StartDate > now)
                                {
                                    return 0; // Henüz başlamamış üyelikler için 0 gün
                                }
                                return (int)Math.Ceiling((ms.EndDate - now).TotalDays);
                            });

                        return new MemberEntryDto
                        {
                            MemberID = g.Key.MemberID,
                            Name = g.Key.Name,
                            PhoneNumber = g.Key.PhoneNumber,
                            EntryTime = g.Key.EntryTime,
                            ExitTime = g.Key.ExitTime,
                            RemainingDays = totalRemainingDays
                        };
                    })
                    .OrderByDescending(x => x.EntryTime);

                // Toplam kayıt sayısını al
                var totalCount = groupedData.Count();

                // Sayfalama uygula
                var items = groupedData
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<MemberEntryDto>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public List<MemberEntryDto> GetMemberEntriesByName(string searchText)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from m in _context.Members
                             join ms in _context.Memberships on m.MemberID equals ms.MemberID
                             join eh in _context.EntryExitHistories on ms.MembershipID equals eh.MembershipID
                             where m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                   && (m.Name.Contains(searchText) || m.PhoneNumber.Contains(searchText))
                                   && eh.EntryDate.HasValue
                                   && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                                   && ms.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                                   && eh.CompanyID == companyId // Giriş-çıkış kayıtlarının da aynı şirkete ait olduğundan emin ol
                             orderby eh.EntryDate descending // Tarihe göre tersten sıralama
                             select new MemberEntryDto
                             {
                                 MemberID = m.MemberID,
                                 Name = m.Name,
                                 PhoneNumber = m.PhoneNumber,
                                 EntryTime = eh.EntryDate.Value,
                                 ExitTime = eh.ExitDate
                             };

                return result.ToList();
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public PaginatedResult<MemberEntryDto> GetMemberEntriesBySearchPaginated(MemberEntryPagingParameters parameters)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var baseQuery = from m in _context.Members
                               join ms in _context.Memberships on m.MemberID equals ms.MemberID
                               join eh in _context.EntryExitHistories on ms.MembershipID equals eh.MembershipID
                               where m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                     && eh.EntryDate.HasValue
                                     && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                                     && ms.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                                     && eh.CompanyID == companyId // Giriş-çıkış kayıtlarının da aynı şirkete ait olduğundan emin ol
                               select new { m, ms, eh };

                // Arama filtresi uygula
                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    baseQuery = baseQuery.Where(x =>
                        x.m.Name.Contains(parameters.SearchText) ||
                        x.m.PhoneNumber.Contains(parameters.SearchText));
                }

                // Tarih filtresi uygula (opsiyonel)
                if (parameters.Date.HasValue)
                {
                    baseQuery = baseQuery.Where(x => x.eh.EntryDate.Value.Date == parameters.Date.Value.Date);
                }

                // DTO'ya dönüştür ve sırala
                var orderedQuery = baseQuery
                    .Select(x => new MemberEntryDto
                    {
                        MemberID = x.m.MemberID,
                        Name = x.m.Name,
                        PhoneNumber = x.m.PhoneNumber,
                        EntryTime = x.eh.EntryDate.Value,
                        ExitTime = x.eh.ExitDate
                    })
                    .OrderByDescending(x => x.EntryTime); // En yeni girişler önce

                // Toplam kayıt sayısını al
                var totalCount = orderedQuery.Count();

                // Sayfalama uygula
                var items = orderedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<MemberEntryDto>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public PaginatedResult<MemberFilter> GetMembersByMultiplePackages(MemberPagingParameters parameters)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var baseQuery = from m in _context.Members
                                join ms in _context.Memberships on m.MemberID equals ms.MemberID
                                join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                where m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                && ms.IsActive == true && ms.EndDate > DateTime.Now && ms.IsFrozen == false
                                && m.CompanyID == companyId
                                && ms.CompanyID == companyId
                                && mt.CompanyID == companyId
                                && parameters.MembershipTypeIds.Contains(ms.MembershipTypeID) // Çoklu paket filtresi
                                select new
                                {
                                    MemberID = m.MemberID,
                                    MembershipID = ms.MembershipID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    Gender = m.Gender,
                                    Branch = mt.Branch,
                                    RemainingDays = ms.StartDate > DateTime.Now
                                        ? EF.Functions.DateDiffDay(ms.StartDate, ms.EndDate)
                                        : EF.Functions.DateDiffDay(DateTime.Now, ms.EndDate),
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate,
                                    IsActive = ms.IsActive == true,
                                    UpdatedDate = ms.UpdatedDate,
                                    CreationDate = ms.CreationDate,
                                    IsFutureStartDate = ms.StartDate > DateTime.Now
                                };

                // Verileri hafızaya alıp gruplandırma yapıyoruz
                var groupedQuery = baseQuery.AsEnumerable()
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.Gender, x.Branch })
                    .Select(g => new MemberFilter
                    {
                        MemberID = g.Key.MemberID,
                        MembershipID = g.Max(x => x.MembershipID),
                        Name = g.Key.Name,
                        PhoneNumber = g.Key.PhoneNumber,
                        Gender = g.Key.Gender,
                        Branch = g.Key.Branch,
                        RemainingDays = g.Sum(x => x.RemainingDays),
                        StartDate = g.Min(x => x.StartDate),
                        EndDate = g.Max(x => x.EndDate),
                        IsActive = true,
                        UpdatedDate = g.Max(x => x.UpdatedDate ?? x.CreationDate),
                        IsFutureStartDate = g.Any(x => x.IsFutureStartDate)
                    });

                // Filtreleme işlemleri
                var filteredQuery = groupedQuery.AsQueryable();

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    var searchText = parameters.SearchText.ToLower();
                    filteredQuery = filteredQuery.Where(x =>
                        x.Name.ToLower().Contains(searchText) ||
                        x.PhoneNumber.Contains(searchText));
                }

                // Cinsiyet filtresi eklendi
                if (parameters.Gender.HasValue)
                {
                    byte genderByte = (byte)parameters.Gender.Value;
                    filteredQuery = filteredQuery.Where(x => x.Gender == genderByte);
                }

                // Sıralama
                var orderedQuery = filteredQuery
                    .OrderByDescending(x => x.UpdatedDate)
                    .ThenByDescending(x => x.MembershipID);

                // Sayfalama için toplam kayıt sayısını al
                var totalCount = orderedQuery.Count();

                // Sayfalama uygula
                var items = orderedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<MemberFilter>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public PaginatedResult<Member> GetMembersWithBalancePaginated(MemberPagingParameters parameters, string balanceFilter)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var query = _context.Members.Where(x => x.IsActive == true && x.CompanyID == companyId);

                // Bakiye filtreleme
                switch (balanceFilter?.ToLower())
                {
                    case "positive":
                        query = query.Where(x => x.Balance > 0);
                        break;
                    case "negative":
                        query = query.Where(x => x.Balance < 0);
                        break;
                    default: // "all" veya diğer durumlar
                        query = query.Where(x => x.Balance != 0); // Sadece bakiyesi olan üyeler
                        break;
                }

                // Arama filtreleme
                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    query = query.Where(x =>
                        x.Name.Contains(parameters.SearchText) ||
                        x.PhoneNumber.Contains(parameters.SearchText));
                }

                // Sıralama - En son güncellenen üyeler en üstte (UpdatedDate'e göre azalan), sonra bakiyeye göre azalan
                query = query.OrderByDescending(x => x.UpdatedDate ?? x.CreationDate).ThenByDescending(x => x.Balance).ThenBy(x => x.Name);

                return query.ToPaginatedResult(parameters.PageNumber, parameters.PageSize);
            }
            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public IDataResult<Dictionary<string, int>> GetBranchCounts(int companyId)
        {
            try
            {
                Dictionary<string, int> branchCounts;

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    branchCounts = (from m in _context.Members
                                    join ms in _context.Memberships on m.MemberID equals ms.MemberID
                                    join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                    where ms.IsActive == true &&
                                          ms.EndDate > DateTime.Now &&
                                          ms.IsFrozen == false &&
                                          m.CompanyID == companyId &&
                                          ms.CompanyID == companyId &&
                                          mt.CompanyID == companyId &&
                                          m.IsActive == true
                                    select new { m.MemberID, mt.Branch })
                                   .GroupBy(x => new { x.MemberID, x.Branch })
                                   .Select(g => new { g.Key.MemberID, g.Key.Branch })
                                   .GroupBy(x => x.Branch)
                                   .ToDictionary(
                                       g => g.Key,
                                       g => g.Count()
                                   );

                    return new SuccessDataResult<Dictionary<string, int>>(branchCounts);
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<Dictionary<string, int>>($"Şube sayıları getirilirken hata oluştu: {ex.Message}");
            }
        }

        public IDataResult<int> GetTotalActiveMembers(int companyId)
        {
            try
            {
                int totalActive;

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    totalActive = _context.Members
                        .Where(m => m.IsActive == true && m.CompanyID == companyId)
                        .Join(_context.Memberships,
                              m => m.MemberID,
                              ms => ms.MemberID,
                              (m, ms) => new { Member = m, Membership = ms })
                        .Where(joined => joined.Membership.IsActive == true &&
                                         joined.Membership.EndDate > DateTime.Now &&
                                         joined.Membership.IsFrozen == false &&
                                         joined.Membership.CompanyID == companyId)
                        .Select(joined => joined.Member.MemberID)
                        .Distinct()
                        .Count();

                    return new SuccessDataResult<int>(totalActive);
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<int>($"Aktif üye sayısı getirilirken hata oluştu: {ex.Message}");
            }
        }

        public IDataResult<int> GetTotalRegisteredMembers(int companyId)
        {
            try
            {
                int totalRegistered;

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    totalRegistered = _context.Members
                        .Where(m => m.IsActive == true && m.CompanyID == companyId)
                        .Count();

                    return new SuccessDataResult<int>(totalRegistered);
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<int>($"Kayıtlı üye sayısı getirilirken hata oluştu: {ex.Message}");
            }
        }

        public IDataResult<Dictionary<string, int>> GetActiveMemberCounts(int companyId)
        {
            try
            {
                Dictionary<string, int> counts;

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    counts = new Dictionary<string, int>();
                    var today = DateTime.Now;

                    // Erkek aktif üye sayısı (Gender = 1)
                    var maleCount = _context.Members
                        .Where(m => m.IsActive == true && m.CompanyID == companyId && m.Gender == 1)
                        .Join(_context.Memberships,
                              m => m.MemberID,
                              ms => ms.MemberID,
                              (m, ms) => new { Member = m, Membership = ms })
                        .Where(joined => joined.Membership.IsActive == true &&
                                         joined.Membership.EndDate > today &&
                                         joined.Membership.IsFrozen == false &&
                                         joined.Membership.CompanyID == companyId)
                        .Select(joined => joined.Member.MemberID)
                        .Distinct()
                        .Count();

                    // Kadın aktif üye sayısı (Gender = 2)
                    var femaleCount = _context.Members
                        .Where(m => m.IsActive == true && m.CompanyID == companyId && m.Gender == 2)
                        .Join(_context.Memberships,
                              m => m.MemberID,
                              ms => ms.MemberID,
                              (m, ms) => new { Member = m, Membership = ms })
                        .Where(joined => joined.Membership.IsActive == true &&
                                         joined.Membership.EndDate > today &&
                                         joined.Membership.IsFrozen == false &&
                                         joined.Membership.CompanyID == companyId)
                        .Select(joined => joined.Member.MemberID)
                        .Distinct()
                        .Count();

                    counts.Add("male", maleCount);
                    counts.Add("female", femaleCount);

                    return new SuccessDataResult<Dictionary<string, int>>(counts);
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<Dictionary<string, int>>($"Aktif üye sayıları getirilirken hata oluştu: {ex.Message}");
            }
        }

        public IDataResult<MemberProfileDto> GetMemberProfileByUserId(int userId, int companyId)
        {
            try
            {
                MemberProfileDto profileDto;

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized

                    // Kullanıcının aktif member kaydını bul (CompanyID'yi buradan al)
                    var userMember = _context.Members
                        .Where(m => m.UserID == userId && m.IsActive == true)
                        .FirstOrDefault();

                    if (userMember == null)
                    {
                        return new ErrorDataResult<MemberProfileDto>("Üyelik bilgileriniz bulunamadı.");
                    }

                    // Kullanıcının gerçek CompanyID'sini kullan
                    int userCompanyId = userMember.CompanyID;

                    var query = from u in _context.Users
                                join m in _context.Members on u.UserID equals m.UserID
                                where u.UserID == userId &&
                                      u.IsActive == true &&
                                      m.IsActive == true &&
                                      m.CompanyID == userCompanyId
                                select new MemberProfileDto
                                {
                                    FirstName = u.FirstName,
                                    LastName = u.LastName,
                                    Email = u.Email,
                                    Adress = m.Adress,
                                    BirthDate = m.BirthDate,
                                    PhoneNumber = m.PhoneNumber,
                                    ProfileImagePath = u.ProfileImagePath
                                };

                    profileDto = query.FirstOrDefault();

                    if (profileDto == null)
                    {
                        return new ErrorDataResult<MemberProfileDto>("Kullanıcı bulunamadı veya üyelik bilgileriniz bulunamadı.");
                    }

                    return new SuccessDataResult<MemberProfileDto>(profileDto, "Profil bilgileri başarıyla getirildi.");
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MemberProfileDto>($"Profil bilgileri getirilirken hata oluştu: {ex.Message}");
            }
        }

        public IResult UpdateMemberProfile(int userId, int companyId, MemberProfileUpdateDto profileUpdateDto)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    // User bilgilerini al ve güncelle
                    var user = _context.Users.FirstOrDefault(u => u.UserID == userId && u.IsActive);
                    if (user == null)
                    {
                        return new ErrorResult("Kullanıcı bulunamadı.");
                    }

                    // Kullanıcının aktif member kaydını al (gerçek CompanyID'si ile)
                    var member = _context.Members.FirstOrDefault(m => m.UserID == userId && m.IsActive == true);
                    if (member == null)
                    {
                        return new ErrorResult("Üyelik bilgileriniz bulunamadı. Lütfen spor salonunuzla iletişime geçin.");
                    }

                    // User tablosunu güncelle (FirstName, LastName)
                    if (!string.IsNullOrWhiteSpace(profileUpdateDto.FirstName))
                    {
                        user.FirstName = profileUpdateDto.FirstName.Trim();
                    }
                    if (!string.IsNullOrWhiteSpace(profileUpdateDto.LastName))
                    {
                        user.LastName = profileUpdateDto.LastName.Trim();
                    }
                    user.UpdatedDate = DateTime.Now;

                    // Member tablosunu güncelle (Adress, BirthDate)
                    member.Adress = profileUpdateDto.Adress?.Trim();
                    member.BirthDate = profileUpdateDto.BirthDate;
                    member.UpdatedDate = DateTime.Now;

                    // Değişiklikleri kaydet
                    _context.SaveChanges();

                    return new SuccessResult("Profil bilgileriniz başarıyla güncellendi.");
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Profil güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public IDataResult<GetMemberQRByPhoneNumberDto> GetMemberQRByUserIdWithoutCompanyFilter(int userId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    // Kullanıcının aktif member kaydını bul (CompanyID filtresi olmadan)
                    var member = _context.Members.FirstOrDefault(m => m.UserID == userId && m.IsActive == true);

                    if (member == null)
                    {
                        return new ErrorDataResult<GetMemberQRByPhoneNumberDto>("Üyelik bilgileriniz bulunamadı. Lütfen spor salonunuzla iletişime geçin.");
                    }

                    // Kullanıcının CompanyID'sini al
                    int userCompanyId = member.CompanyID;

                    // Üyelikleri al
                    var allMemberships = _context.Memberships
                        .Where(m => m.MemberID == member.MemberID && m.IsActive == true && m.CompanyID == userCompanyId)
                        .OrderBy(m => m.StartDate)
                        .ToList();

                    var membershipTypes = _context.MembershipTypes
                        .Where(mt => mt.CompanyID == userCompanyId)
                        .ToDictionary(mt => mt.MembershipTypeID);

                    // Dondurulmuş üyelik kontrolü
                    var frozenMembership = allMemberships.FirstOrDefault(m => m.IsFrozen == true);
                    if (frozenMembership != null)
                    {
                        return new SuccessDataResult<GetMemberQRByPhoneNumberDto>(
                            new GetMemberQRByPhoneNumberDto
                            {
                                Name = member.Name,
                                ScanNumber = member.ScanNumber,
                                IsFrozen = true,
                                FreezeEndDate = frozenMembership.FreezeEndDate,
                                RemainingDays = "Dondurulmuş",
                                Memberships = new List<MembershipInfo>(),
                                PhoneNumber = member.PhoneNumber
                            },
                            $"Üyeliğiniz dondurulmuştur. Açılış tarihi: {frozenMembership.FreezeEndDate?.ToString("dd/MM/yyyy")}"
                        );
                    }

                    // Aktif üyelikleri işle
                    var consolidatedMemberships = new Dictionary<string, MembershipInfo>();
                    var now = DateTime.Now;

                    foreach (var membership in allMemberships.Where(m => m.EndDate > now))
                    {
                        if (membershipTypes.TryGetValue(membership.MembershipTypeID, out var membershipType))
                        {
                            var branch = membershipType.Branch;
                            var remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);

                            if (consolidatedMemberships.ContainsKey(branch))
                            {
                                consolidatedMemberships[branch].RemainingDays += remainingDays;
                            }
                            else
                            {
                                consolidatedMemberships[branch] = new MembershipInfo
                                {
                                    Branch = branch,
                                    RemainingDays = remainingDays,
                                    StartDate = membership.StartDate,
                                    EndDate = membership.EndDate
                                };
                            }
                        }
                    }

                    var message = consolidatedMemberships.Count > 0
                        ? $"Hoşgeldiniz {member.Name.ToUpper()}. Toplam {consolidatedMemberships.Values.Sum(m => m.RemainingDays)} gün kaldı."
                        : $"Hoşgeldiniz {member.Name.ToUpper()}. Üyeliğinizin süresi dolmuş.";

                    return new SuccessDataResult<GetMemberQRByPhoneNumberDto>(
                        new GetMemberQRByPhoneNumberDto
                        {
                            Name = member.Name,
                            ScanNumber = member.ScanNumber, // QR şifreleme Manager'da yapılacak
                            RemainingDays = string.Join(", ", consolidatedMemberships.Values
                                .OrderByDescending(m => m.RemainingDays)
                                .Select(m => $"{m.Branch}: {m.RemainingDays} Gün")),
                            Memberships = consolidatedMemberships.Values.ToList(),
                            IsFrozen = false,
                            FreezeEndDate = null,
                            PhoneNumber = member.PhoneNumber
                        },
                        message
                    );
                }
                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<GetMemberQRByPhoneNumberDto>($"QR kod bilgileri getirilirken hata oluştu: {ex.Message}");
            }
        }

        public IDataResult<GetMemberQRByPhoneNumberDto> GetMemberQRByPhoneNumber(string phoneNumber, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    // Telefon numarası ile üye ara (IsActive=true olan üyelerde)
                    var member = _context.Members.FirstOrDefault(m => m.PhoneNumber == phoneNumber && m.IsActive == true);

                    if (member == null)
                    {
                        return new ErrorDataResult<GetMemberQRByPhoneNumberDto>("Telefon Numarasını Kontrol Ediniz.");
                    }

                    // Üyenin CompanyID'sini al
                    int memberCompanyId = member.CompanyID;

                    var now = DateTime.Now;

                    // Üyelikleri al
                    var allMemberships = _context.Memberships
                        .Where(m => m.MemberID == member.MemberID && m.IsActive == true && m.CompanyID == memberCompanyId)
                        .OrderBy(m => m.StartDate)
                        .ToList();

                    var membershipTypes = _context.MembershipTypes
                        .Where(mt => mt.CompanyID == memberCompanyId)
                        .ToDictionary(mt => mt.MembershipTypeID);

                    // Dondurulmuş üyelik kontrolü
                    var frozenMembership = allMemberships.FirstOrDefault(m => m.IsFrozen == true);
                    if (frozenMembership != null)
                    {
                        return new SuccessDataResult<GetMemberQRByPhoneNumberDto>(
                            new GetMemberQRByPhoneNumberDto
                            {
                                Name = member.Name,
                                ScanNumber = member.ScanNumber,
                                IsFrozen = true,
                                FreezeEndDate = frozenMembership.FreezeEndDate,
                                RemainingDays = "Dondurulmuş",
                                Memberships = new List<MembershipInfo>(),
                                PhoneNumber = member.PhoneNumber
                            },
                            $"Üyelik dondurulmuştur. Açılış tarihi: {frozenMembership.FreezeEndDate?.ToString("dd/MM/yyyy")}"
                        );
                    }

                    // Aktif üyelikleri işle
                    var consolidatedMemberships = new Dictionary<string, MembershipInfo>();

                    foreach (var membership in allMemberships)
                    {
                        if (membershipTypes.TryGetValue(membership.MembershipTypeID, out var membershipType))
                        {
                            var branch = membershipType.Branch;
                            var remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);

                            if (consolidatedMemberships.ContainsKey(branch))
                            {
                                if (membership.StartDate < consolidatedMemberships[branch].StartDate)
                                    consolidatedMemberships[branch].StartDate = membership.StartDate;
                                if (membership.EndDate > consolidatedMemberships[branch].EndDate)
                                    consolidatedMemberships[branch].EndDate = membership.EndDate;
                                consolidatedMemberships[branch].RemainingDays += Math.Max(0, remainingDays);
                            }
                            else
                            {
                                consolidatedMemberships[branch] = new MembershipInfo
                                {
                                    Branch = branch,
                                    StartDate = membership.StartDate,
                                    EndDate = membership.EndDate,
                                    RemainingDays = Math.Max(0, remainingDays)
                                };
                            }
                        }
                    }

                    var message = consolidatedMemberships.Count > 0 && consolidatedMemberships.Values.Any(m => m.RemainingDays > 0)
                        ? $"Hoşgeldiniz {member.Name.ToUpper()}. Toplam {consolidatedMemberships.Values.Sum(m => m.RemainingDays)} gün kaldı."
                        : $"Hoşgeldiniz {member.Name.ToUpper()}. Üyeliğinizin süresi dolmuş.";

                    return new SuccessDataResult<GetMemberQRByPhoneNumberDto>(
                        new GetMemberQRByPhoneNumberDto
                        {
                            Name = member.Name,
                            ScanNumber = member.ScanNumber, // QR şifreleme Manager'da yapılacak
                            RemainingDays = consolidatedMemberships.Count > 0
                                ? consolidatedMemberships.Values.Sum(m => m.RemainingDays).ToString()
                                : "Süresi Dolmuş",
                            Memberships = consolidatedMemberships.Values.ToList(),
                            IsFrozen = false,
                            FreezeEndDate = null,
                            PhoneNumber = member.PhoneNumber,
                            Message = message
                        },
                        message
                    );
                }
                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<GetMemberQRByPhoneNumberDto>($"QR kod bilgileri getirilirken hata oluştu: {ex.Message}");
            }
        }

        public IDataResult<MemberDetailDto> GetMemberRemainingDaysForScanNumber(string scanNumber, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    // ScanNumber ile üye ara
                    var member = _context.Members.FirstOrDefault(m => m.ScanNumber == scanNumber && m.IsActive == true);

                    if (member == null)
                    {
                        return new ErrorDataResult<MemberDetailDto>("Geçersiz QR kod.");
                    }

                    // Üyenin CompanyID'sini al
                    int memberCompanyId = member.CompanyID;

                    var now = DateTime.Now;

                    // Üyelikleri al
                    var allMemberships = _context.Memberships
                        .Where(m => m.MemberID == member.MemberID && m.IsActive == true && m.CompanyID == memberCompanyId)
                        .ToList();

                    var membershipTypes = _context.MembershipTypes
                        .Where(mt => mt.CompanyID == memberCompanyId)
                        .ToDictionary(mt => mt.MembershipTypeID);

                    // Dondurulmuş üyelik kontrolü
                    var frozenMembership = allMemberships.FirstOrDefault(m => m.IsFrozen == true);
                    if (frozenMembership != null)
                    {
                        return new SuccessDataResult<MemberDetailDto>(
                            new MemberDetailDto
                            {
                                MemberID = member.MemberID,
                                MemberName = member.Name,
                                PhoneNumber = member.PhoneNumber,
                                Message = $"Üyelik dondurulmuştur. Açılış tarihi: {frozenMembership.FreezeEndDate?.ToString("dd/MM/yyyy")}",
                                Memberships = new List<MembershipDetailDto>()
                            },
                            $"Üyelik dondurulmuştur. Açılış tarihi: {frozenMembership.FreezeEndDate?.ToString("dd/MM/yyyy")}"
                        );
                    }

                    // Aktif üyelikleri işle
                    var membershipDetails = new List<MembershipDetailDto>();

                    foreach (var membership in allMemberships.Where(m => m.EndDate > now))
                    {
                        if (membershipTypes.TryGetValue(membership.MembershipTypeID, out var membershipType))
                        {
                            var remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);

                            membershipDetails.Add(new MembershipDetailDto
                            {
                                Branch = membershipType.Branch,
                                StartDate = membership.StartDate,
                                EndDate = membership.EndDate,
                                RemainingDays = remainingDays
                            });
                        }
                    }

                    var totalRemainingDays = membershipDetails.Sum(m => m.RemainingDays);
                    var message = totalRemainingDays > 0
                        ? $"Hoşgeldiniz {member.Name.ToUpper()}. Toplam {totalRemainingDays} gün kaldı."
                        : $"Hoşgeldiniz {member.Name.ToUpper()}. Üyeliğinizin süresi dolmuş.";

                    return new SuccessDataResult<MemberDetailDto>(
                        new MemberDetailDto
                        {
                            MemberID = member.MemberID,
                            MemberName = member.Name,
                            PhoneNumber = member.PhoneNumber,
                            Message = message,
                            Memberships = membershipDetails
                        },
                        message
                    );
                }
                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MemberDetailDto>($"QR kod işlenirken hata oluştu: {ex.Message}");
            }
        }

        // SOLID prensiplerine uygun refactoring için eklenen metotlar

        public IResult AddMemberWithUserManagement(Member member, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    return AddMemberWithUserManagementInternal(member, companyId, _context);
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üye eklenirken hata oluştu: {ex.Message}");
            }
        }

        public IResult AddMemberWithCard(Member member, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Kartlı üye ekleme
                    return AddMemberWithCardInternal(member, companyId, _context);
                }

                // DI kullanılmıyorsa exception fırlat
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Kartlı üye eklenirken hata oluştu: {ex.Message}");
            }
        }

        private IResult AddMemberWithUserManagementInternal(Member member, int companyId, GymContext context)
        {
            // CompanyID ata
            member.CompanyID = companyId;
            member.CreationDate = DateTime.Now;
            member.IsActive = true;

            // QR kod üret
            member.ScanNumber = GenerateUniqueQRCode(context);

            // E-posta adresi kontrolü
            if (!string.IsNullOrEmpty(member.Email))
            {
                var existingUser = context.Users.FirstOrDefault(u => u.Email == member.Email);
                if (existingUser != null)
                {
                    // Kullanıcı bulundu, Member ile ilişkilendir
                    member.UserID = existingUser.UserID;
                }
                else
                {
                    // Kullanıcı bulunamadı, otomatik kullanıcı hesabı oluştur
                    var newUser = CreateNewUserForMember(member, context);
                    if (newUser != null)
                    {
                        member.UserID = newUser.UserID;
                    }
                }
            }

            context.Members.Add(member);
            context.SaveChanges();
            return new SuccessResult("Üye başarıyla eklendi.");
        }

        private IResult AddMemberWithCardInternal(Member member, int companyId, GymContext context)
        {
            // CompanyID ata
            member.CompanyID = companyId;
            member.CreationDate = DateTime.Now;
            member.IsActive = true;

            // Kart numarası kontrolü - frontend'den gelen cardNumber'ı ScanNumber'a ata
            if (string.IsNullOrEmpty(member.ScanNumber))
            {
                return new ErrorResult("Kart numarası boş olamaz.");
            }

            // Kart numarası benzersizlik kontrolü
            var existingMemberWithCard = context.Members
                .FirstOrDefault(m => m.ScanNumber == member.ScanNumber && m.CompanyID == companyId);

            if (existingMemberWithCard != null)
            {
                return new ErrorResult("Bu kart numarası zaten başka bir üye tarafından kullanılıyor.");
            }

            // E-posta adresi kontrolü (kartlı sistemde opsiyonel)
            if (!string.IsNullOrEmpty(member.Email))
            {
                var existingUser = context.Users.FirstOrDefault(u => u.Email == member.Email);
                if (existingUser != null)
                {
                    // Kullanıcı bulundu, Member ile ilişkilendir
                    member.UserID = existingUser.UserID;
                }
                else
                {
                    // Kullanıcı bulunamadı, otomatik kullanıcı hesabı oluştur
                    var newUser = CreateNewUserForMember(member, context);
                    if (newUser != null)
                    {
                        member.UserID = newUser.UserID;
                    }
                }
            }
            // Kartlı sistemde e-posta yoksa UserID null kalır (mobil uygulama erişimi yok)

            // Telefon numarası benzersizlik kontrolü
            var existingMemberWithPhone = context.Members
                .FirstOrDefault(m => m.PhoneNumber == member.PhoneNumber && m.CompanyID == companyId);

            if (existingMemberWithPhone != null)
            {
                return new ErrorResult("Bu telefon numarası zaten kayıtlı.");
            }

            // Üyeyi veritabanına ekle
            context.Members.Add(member);
            context.SaveChanges();

            return new SuccessResult("Kartlı üye başarıyla eklendi.");
        }

        public IResult DeleteMemberWithUserManagement(int memberId, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    return DeleteMemberWithUserManagementInternal(memberId, companyId, _context);
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üye silinirken hata oluştu: {ex.Message}");
            }
        }

        private IResult DeleteMemberWithUserManagementInternal(int memberId, int companyId, GymContext context)
        {
            var member = context.Members.FirstOrDefault(m => m.MemberID == memberId && m.CompanyID == companyId);
            if (member == null)
            {
                return new ErrorResult("Üye bulunamadı veya erişim yetkiniz yok.");
            }

            // Soft delete
            member.IsActive = false;
            member.DeletedDate = DateTime.Now;

            // Eğer bu Member'ın UserID'si varsa, diğer aktif Member kayıtlarını kontrol et
            if (member.UserID.HasValue)
            {
                // Bu User'ın başka aktif Member kayıtları var mı kontrol et
                var otherActiveMembers = context.Members
                    .Where(m => m.UserID == member.UserID &&
                               m.MemberID != memberId &&
                               m.IsActive == true)
                    .ToList();

                // Eğer başka aktif Member'ı yoksa RequirePasswordChange=true yap
                if (otherActiveMembers.Count == 0)
                {
                    var user = context.Users.FirstOrDefault(u => u.UserID == member.UserID.Value);
                    if (user != null)
                    {
                        user.RequirePasswordChange = true;
                        user.UpdatedDate = DateTime.Now;
                    }
                }
            }

            context.SaveChanges();
            return new SuccessResult("Üye başarıyla silindi.");
        }

        public IDataResult<MemberDetailWithHistoryDto> GetMemberDetailByIdWithCalculations(int memberId, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    return GetMemberDetailByIdWithCalculationsInternal(memberId, companyId, _context);
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MemberDetailWithHistoryDto>($"Üye detayı getirilirken hata oluştu: {ex.Message}");
            }
        }

        private IDataResult<MemberDetailWithHistoryDto> GetMemberDetailByIdWithCalculationsInternal(int memberId, int companyId, GymContext context)
        {
            // Önce member'ın bu company'ye ait olup olmadığını kontrol et
            var member = context.Members.FirstOrDefault(m => m.MemberID == memberId && m.CompanyID == companyId);
            if (member == null)
            {
                return new ErrorDataResult<MemberDetailWithHistoryDto>("Üye bulunamadı veya erişim yetkiniz yok.");
            }

            var memberDetail = GetMemberDetailById(memberId);
            if (memberDetail == null)
            {
                return new ErrorDataResult<MemberDetailWithHistoryDto>("Üye detayı bulunamadı.");
            }

            // Aktif ve dondurulmamış üyeliklerden bitiş tarihi en ileri olanı bul
            var activeMembership = memberDetail.Memberships?
                .Where(m => m.IsActive && !m.IsFrozen && m.EndDate > DateTime.Now)
                .OrderByDescending(m => m.EndDate)
                .FirstOrDefault();

            if (activeMembership != null)
            {
                var remainingTime = activeMembership.EndDate - DateTime.Now;
                memberDetail.RemainingDays = (int)Math.Ceiling(remainingTime.TotalDays);
            }
            else
            {
                memberDetail.RemainingDays = null; // Aktif üyelik yoksa null ata
            }

            return new SuccessDataResult<MemberDetailWithHistoryDto>(memberDetail);
        }

        public IResult UpdateMemberWithUserManagement(Member member, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    return UpdateMemberWithUserManagementInternal(member, companyId, _context);
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üye güncellenirken hata oluştu: {ex.Message}");
            }
        }

        private IResult UpdateMemberWithUserManagementInternal(Member member, int companyId, GymContext context)
        {
            // Mevcut üye bilgilerini al - CompanyID kontrolü ile güvenli erişim
            var existingMember = context.Members.FirstOrDefault(m => m.MemberID == member.MemberID && m.CompanyID == companyId);
            if (existingMember == null)
            {
                return new ErrorResult("Üye bulunamadı veya erişim yetkiniz yok.");
            }

            // Güvenlik için CompanyID'yi tekrar ata
            member.CompanyID = companyId;

            // E-posta adresi kontrolü
            if (!string.IsNullOrEmpty(member.Email))
            {
                // E-posta değişmiş mi kontrol et
                if (existingMember.Email != member.Email)
                {
                    // Üyenin mevcut bir UserID'si var mı kontrol et
                    if (existingMember.UserID.HasValue)
                    {
                        // Mevcut User kaydını al
                        var existingUser = context.Users.FirstOrDefault(u => u.UserID == existingMember.UserID.Value);
                        if (existingUser != null)
                        {
                            // Mevcut User kaydını güncelle
                            existingUser.Email = member.Email;

                            // Ad ve soyad güncelleme (boşluğa göre)
                            string firstName = member.Name;
                            string lastName = "";

                            int spaceIndex = member.Name.IndexOf(' ');
                            if (spaceIndex > 0)
                            {
                                firstName = member.Name.Substring(0, spaceIndex);
                                lastName = member.Name.Substring(spaceIndex + 1);
                            }

                            existingUser.FirstName = firstName;
                            existingUser.LastName = lastName;
                            existingUser.UpdatedDate = DateTime.Now;

                            // Member ile User ilişkisini koru
                            member.UserID = existingMember.UserID;
                        }
                        else
                        {
                            // Mevcut User kaydı bulunamadı, yeni bir User kaydı oluştur
                            var newUser = CreateNewUserForMember(member, context);
                            if (newUser != null)
                            {
                                member.UserID = newUser.UserID;
                            }
                        }
                    }
                    else
                    {
                        // Kullanıcı sistemde kayıtlı mı kontrol et
                        var existingUser = context.Users.FirstOrDefault(u => u.Email == member.Email);
                        if (existingUser != null)
                        {
                            // Kullanıcı bulundu, Member ile ilişkilendir
                            member.UserID = existingUser.UserID;
                        }
                        else
                        {
                            // Kullanıcı bulunamadı, otomatik kullanıcı hesabı oluştur
                            var newUser = CreateNewUserForMember(member, context);
                            if (newUser != null)
                            {
                                member.UserID = newUser.UserID;
                            }
                        }
                    }
                }
                else
                {
                    // E-posta değişmemiş, mevcut UserID'yi koru
                    member.UserID = existingMember.UserID;
                }
            }
            else
            {
                // E-posta boş ise UserID'yi null yap
                member.UserID = null;
            }

            // Mevcut entity'yi manuel olarak güncelle (Update yerine)
            existingMember.Name = member.Name;
            existingMember.Email = member.Email;
            existingMember.PhoneNumber = member.PhoneNumber;
            existingMember.Adress = member.Adress;
            existingMember.BirthDate = member.BirthDate;
            existingMember.Gender = member.Gender;
            existingMember.UserID = member.UserID;
            existingMember.Balance = member.Balance; // Balance alanı eklendi
            existingMember.UpdatedDate = DateTime.Now;

            context.SaveChanges();
            return new SuccessResult("Üye başarıyla güncellendi.");
        }

        // Helper metotlar
        private string GenerateUniqueQRCode(GymContext context)
        {
            string qrCode;
            do
            {
                var prefix = "MBR";
                var timestamp = DateTime.Now.ToString("yyMMddHH");
                var randomPart = GenerateRandomPart(12);
                qrCode = $"{prefix}{timestamp}{randomPart}";
            } while (context.Members.Any(m => m.ScanNumber == qrCode));

            return qrCode;
        }

        private string GenerateRandomPart(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            byte[] data = new byte[length];
            using (var rng = new RNGCryptoServiceProvider())
            {
                rng.GetBytes(data);
            }
            return new string(data.Select(b => chars[b % chars.Length]).ToArray());
        }

        private User CreateNewUserForMember(Member member, GymContext context)
        {
            if (string.IsNullOrEmpty(member.PhoneNumber) || member.PhoneNumber.Length < 4)
                return null;

            // Telefon numarasının son 4 hanesini geçici şifre olarak kullan
            string tempPassword = member.PhoneNumber.Substring(member.PhoneNumber.Length - 4);

            // Ad ve soyad ayırma (boşluğa göre)
            string firstName = member.Name;
            string lastName = "";

            int spaceIndex = member.Name.IndexOf(' ');
            if (spaceIndex > 0)
            {
                firstName = member.Name.Substring(0, spaceIndex);
                lastName = member.Name.Substring(spaceIndex + 1);
            }

            // Kullanıcı oluştur
            byte[] passwordHash, passwordSalt;
            HashingHelper.CreatePasswordHash(tempPassword, out passwordHash, out passwordSalt);

            var user = new User
            {
                Email = member.Email,
                FirstName = firstName,
                LastName = lastName,
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                IsActive = true,
                RequirePasswordChange = true, // Şifre değiştirme zorunluluğu
                CreationDate = DateTime.Now
            };

            context.Users.Add(user);
            context.SaveChanges();

            // "member" rolünü al ve ata
            var memberRole = context.OperationClaims.FirstOrDefault(oc => oc.Name == "member");
            if (memberRole != null)
            {
                var userOperationClaim = new UserOperationClaim
                {
                    UserId = user.UserID,
                    OperationClaimId = memberRole.OperationClaimId,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };
                context.UserOperationClaims.Add(userOperationClaim);
                context.SaveChanges();
            }

            return user;
        }
    } // EfMemberDal sınıfı kapanışı
} // namespace kapanışı
