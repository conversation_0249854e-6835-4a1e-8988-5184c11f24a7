﻿using Business.Abstract;
using Entities.Concrete;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class LicensePackagesController : ControllerBase
    {
        private readonly ILicensePackageService _licensePackageService;

        public LicensePackagesController(ILicensePackageService licensePackageService)
        {
            _licensePackageService = licensePackageService;
        }

        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _licensePackageService.GetAll();
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getbyid")]
        public IActionResult GetById(int id)
        {
            var result = _licensePackageService.GetById(id);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("add")]
        public IActionResult Add(LicensePackage licensePackage)
        {
            var result = _licensePackageService.Add(licensePackage);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("update")]
        public IActionResult Update(LicensePackage licensePackage)
        {
            var result = _licensePackageService.Update(licensePackage);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _licensePackageService.Delete(id);
            return result.Success ? Ok(result) : BadRequest(result);
        }
    }
}